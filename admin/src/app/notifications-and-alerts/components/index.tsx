"use client";;
import { AdminAlertQueryResponse, AdminNotificationSubscriptionQueryResponse } from "@rubiconcarbon/shared-types";
import Page from "@components/layout/containers/page";
import NotificationsAndAlertsComponent from "./notifications-and-alerts";

import type { JSX } from "react";

export default function NotificationsAndAlerts({
  notificationSubsResponse,
  alertSubsResponse,
}: {
  notificationSubsResponse: AdminNotificationSubscriptionQueryResponse;
  alertSubsResponse: AdminAlertQueryResponse;
}): JSX.Element {
  return (
    <Page>
      <NotificationsAndAlertsComponent notificationSubsResponse={notificationSubsResponse} alertSubsResponse={alertSubsResponse} />
    </Page>
  );
}
