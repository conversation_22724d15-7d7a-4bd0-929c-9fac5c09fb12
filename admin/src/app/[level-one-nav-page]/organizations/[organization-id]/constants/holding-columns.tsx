import { GenericTableFieldSizeEnum } from "@components/ui/generic-table/constants/generic-table-field-size.enum";
import { GenericTableColumn } from "@components/ui/generic-table/types/generic-table-column";
import { AssetType, uuid } from "@rubiconcarbon/shared-types";
import ProductName from "@components/ui/product-name/product-name";
import Decimal from "decimal.js";

import type { JSX } from "react";

export type HoldingModel = {
  id: uuid;
  productF: string;
  type: string;
  amountAllocated: number;
  fop: number;
  portfolioName?: string;
  vintageName?: string;
  projectId?: uuid;
  projectName?: string;
  registryProjectId?: string;
  riskBufferPercentage?: Decimal;
  rctStandard?: boolean;
  isScienceTeamApproved?: boolean;
  suspended?: boolean;
  isRctEligible?: boolean;
};

export const HOLDING_COLUMNS: GenericTableColumn<HoldingModel>[] = [
  {
    field: "productF",
    label: "Product",
    width: GenericTableFieldSizeEnum.large,
    maxWidth: GenericTableFieldSizeEnum.xxxlarge,
    renderDataCell: (row): JSX.Element => {
      const isRctVintage = row?.type === "Project";

      return (
        <ProductName
          assets={[
            (isRctVintage
              ? {
                  name: row?.projectName,
                  type: AssetType.REGISTRY_VINTAGE,
                  projectId: row?.projectId,
                  registryProjectId: row?.registryProjectId,
                  projectVintageName: row?.vintageName,
                  riskBufferPercentage: row?.riskBufferPercentage,
                  isRctEligible: row.isRctEligible,
                  isSuspended: row?.suspended,
                  isRctStandard: row?.rctStandard,
                  isScienceTeamApproved: row?.isScienceTeamApproved,
                  projectVintage: {
                    name: row?.vintageName,
                    riskBufferPercentage: row?.riskBufferPercentage,
                    project: {
                      id: row?.projectId,
                      name: row?.projectName,
                      registryProjectId: row?.registryProjectId,
                      suspended: row?.suspended,
                      rctStandard: row?.rctStandard,
                      isScienceTeamApproved: row?.isScienceTeamApproved,
                    },
                  },
                }
              : {
                  id: row?.id,
                  name: row?.portfolioName,
                  type: AssetType.RCT,
                }) as any,
          ]}
          style={{ fontSize: 14 }}
        />
      );
    },
  },
  {
    field: "type",
    label: "Type",
    width: GenericTableFieldSizeEnum.large,
    maxWidth: GenericTableFieldSizeEnum.xlarge,
  },
  {
    field: "amountAllocated",
    label: "Holdings",
    type: "number",
    width: GenericTableFieldSizeEnum.large,
    fixedWidth: true,
  },
  {
    field: "fop" as any, // fraction of portfolio (fop)
    label: "% of Portfolio",
    type: "percentage",
    width: GenericTableFieldSizeEnum.medium,
    fixedWidth: true,
  },
];
