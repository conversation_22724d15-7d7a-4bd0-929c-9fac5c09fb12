"use client";;
import Page from "@components/layout/containers/page";
import ProjectUpload from "./project-upload";
import { AdminProjectQueryResponse } from "@rubiconcarbon/shared-types";

import type { JSX } from "react";

const ImageManagement = ({ projectsResponse }: { projectsResponse: AdminProjectQueryResponse }): JSX.Element => {
  return (
    <Page>
      <ProjectUpload projectsResponse={projectsResponse} />
    </Page>
  );
};

export default ImageManagement;
