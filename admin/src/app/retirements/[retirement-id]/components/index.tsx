"use client";;
import Page from "@components/layout/containers/page";
import { AdminRetirementResponse } from "@rubiconcarbon/shared-types";
import RetirementItemComponent from "./retirement-item";

import type { JSX } from "react";

const RetirementItem = ({ retirementResponse }: { retirementResponse: AdminRetirementResponse }): JSX.Element => {
  return (
    <Page>
      <RetirementItemComponent retirementResponse={retirementResponse} />
    </Page>
  );
};

export default RetirementItem;
