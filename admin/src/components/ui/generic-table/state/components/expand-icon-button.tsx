import { useAtomValue, useSetAtom } from "jotai";
import { expandedRowsAtom, updateExpandedRowsAtom } from "../actions";
import { externalAtom } from "../structure";
import { IconButton } from "@mui/material";
import { GenericTableRowModel } from "../../types/generic-table-row-model";
import { MouseEvent, type JSX } from "react";
import MatIcon from "@components/ui/mat-icon/mat-icon";

type ExpandIconButtonProps<M> = {
  row: GenericTableRowModel<M>;
};

const ExpandIconButton = ({ row }: ExpandIconButtonProps<any>): JSX.Element => {
  const record = useAtomValue(expandedRowsAtom);
  const { expandOn } = useAtomValue(externalAtom);

  const set = useSetAtom(updateExpandedRowsAtom);

  const expanded = record?.[row?.id];
  const shouldExpand = expanded || (row?.creating && expandOn?.create) || (row?.editing && expandOn?.edit);

  return (
    <IconButton
      size="small"
      disabled={row?.creating || row?.editing}
      onClick={(event: MouseEvent<HTMLButtonElement>) => {
        event?.preventDefault();
        event?.stopPropagation();

        set({ id: row?.id });
      }}
    >
      {shouldExpand ? (
        <MatIcon value="keyboard_arrow_up" variant="round" color="inherit" />
      ) : (
        <MatIcon value="keyboard_arrow_down" variant="round" color="inherit" />
      )}
    </IconButton>
  );
};

export default ExpandIconButton;
