"use client";

import React, {
  createContext,
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useState,
  type JSX,
} from "react";
import { GoogleLogin, CredentialResponse } from "@react-oauth/google";
import Image from "next/image";
import { useAppRouter } from "@hooks/router-hooks";
import { PermissionEnum } from "@rubiconcarbon/shared-types";
import { APIAuthResponse, decodeUser, User } from "@app/libs/auth-helpers";

import logo from "@assets/images/RC-Admin-Logo.svg";

export interface UserContextType {
  user?: User;
  loading: boolean;
  error?: any;
  logout: () => void;
  token?: string;
}

export const AuthContext = createContext<UserContextType>({ loading: true, logout: () => {} });

export function AuthProvider({ children }: { children: JSX.Element }): JSX.Element {
  const [user, setUser] = useState<User>();
  const [token, setToken] = useState<string>();
  const [error, setError] = useState<any>();
  const [loading, setLoading] = useState(false);
  const [initialAttempted, setInitialAttempted] = useState(false);

  const router = useAppRouter();

  useEffect(() => {
    if (token) {
      localStorage.setItem("platform-tkn", Buffer.from(JSON.stringify(token)).toString("base64"));
    } else {
      localStorage.removeItem("platform-tkn");
    }
  }, [token]);

  const logout = useCallback(() => {
    fetch("/auth/cookie", { method: "DELETE" });
    setUser(undefined);
    setToken(undefined);
    router.push("/");
  }, [router]);

  useEffect(() => {
    if (error) setError(null);
  }, [error]);

  useEffect(() => {
    async function fetchUser(): Promise<void> {
      try {
        const res = await fetch("/auth/cookie");

        if (!res.ok) throw new Error("Not authenticated");

        const data: APIAuthResponse = await res.json();

        const user = decodeUser(data);

        if (user) {
          user.permissions = data.permissions;
          setUser(user);
          setToken(data.token);
        }
      } catch {
        logout();
      } finally {
        setInitialAttempted(true);
      }
    }

    fetchUser();
  }, [logout]);

  async function auth(credentialResponse: CredentialResponse): Promise<void> {
    setLoading(true);

    try {
      const apiResponse = await fetch(process.env.NEXT_PUBLIC_AUTH_URL || "/api/auth", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          token: credentialResponse.credential,
          type: "google",
          permissions: Object.values(PermissionEnum),
        }),
      });

      const data: APIAuthResponse = await apiResponse.json();

      const user = decodeUser(data);

      await fetch("/auth/cookie", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(data),
      });

      if (user) {
        user.permissions = data.permissions;
        setUser(user);
        setToken(data.token);
      }
    } catch {
      logout();
    } finally {
      setLoading(false);
    }

    try {
      const apiResponse = await fetch(process.env.NEXT_PUBLIC_AUTH_URL || "/api/auth", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          token: credentialResponse.credential,
          type: "google",
          permissions: Object.values(PermissionEnum),
        }),
      });

      const data: APIAuthResponse = await apiResponse.json();

      const user = decodeUser(data);

      await fetch("/auth/cookie", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(data),
      });

      if (user) {
        user.permissions = data.permissions;
        setUser(user);
        setToken(data.token);
      }
    } catch {
      logout();
    } finally {
      setLoading(false);
    }
  }

  const contextValue = useMemo(
    () => ({
      user,
      loading,
      error,
      logout,
      token,
    }),
    [user, loading, error, logout, token],
  );

  if (!initialAttempted) return <></>;

  return (
    <AuthContext.Provider value={contextValue}>
      {user ? (
        children
      ) : (
        <div
          style={{
            display: "flex",
            flexDirection: "column",
            alignItems: "center",
            justifyContent: "center",
            height: "100vh",
          }}
        >
          <Image src={logo} alt="Rubicon Carbon" priority width={270} className="mb-4" />
          <GoogleLogin
            size="large"
            shape="pill"
            auto_select
            onSuccess={auth}
            onError={() => console.error("Login Failed")}
          />
        </div>
      )}
    </AuthContext.Provider>
  );
}

export default function useAuth(): UserContextType {
  return useContext(AuthContext);
}
