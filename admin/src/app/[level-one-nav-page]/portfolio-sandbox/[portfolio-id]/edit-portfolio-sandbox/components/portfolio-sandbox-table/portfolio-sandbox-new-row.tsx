import React, { useState, useC<PERSON>back, useContext, useMemo, useEffect, type JSX } from "react";
import useS<PERSON> from "swr";
import { Stack, IconButton, TableCell, TableRow, createFilterOptions, Box } from "@mui/material";
import DeleteIcon from "@mui/icons-material/Delete";
import CheckIcon from "@mui/icons-material/Check";
import {
  uuid,
  AdminProjectVintageResponse,
  AdminVintagePricingResponse,
  AdminProjectVintageQueryResponse,
  AdminProjectResponse,
  AdminBufferCategoryResponse,
  ProjectVintageRelations,
} from "@rubiconcarbon/shared-types";
import { AxiosContext } from "@providers/axios-provider";
import { isEmpty } from "lodash";
import integerFormat from "@/utils/formatters/integer-format";
import {
  ColDef,
  PortfolioRow,
  Project,
  Vintage,
  VintageFormField,
  getInventoryShortage,
  isValidRow,
  getEmptyPortfolioRow,
  validateRow,
  convertStringToNumber,
  handleQuantityChange,
  getAmountAvailable,
  BufferCategory,
  BufferCategoryField,
  ProjectPopulationType,
  PortfolioComponentResponse,
  getHoldingAmount,
} from "./portfolio-sandbox-model";
import Decimal from "decimal.js";
import BaseRow from "../infra/portfolio-sandbox-row";
import { currencyFormat, generateQueryParams, Nullable, percentageFormat } from "@rubiconcarbon/frontend-shared";
import { MISSING_DATA, SERVER_PAGINATION_LIMIT } from "@constants/constants";
import { Maybe } from "@rubiconcarbon/frontend-shared";
import decimalFormat from "@/utils/formatters/decimal-format";
import { createQueryFromFilter, FilterData } from "../portfolio-sandbox-filter/filter-model";
import IntegrityScore from "@components/integrity-score/integrity-score-chart";
import InventoryShortage from "./inventory-shortage";

import classes from "../../styles/styles.module.scss";

const INITIAL_SEARCH_PHRASE = "a";

export default function NewRow(props: {
  availableComponents: PortfolioComponentResponse[];
  bufferCategories: AdminBufferCategoryResponse[];
  submitRow: (updatedRow: PortfolioRow, isRestricted: boolean) => void;
  cancelAdd: () => void;
  isRestricted?: boolean;
  projectsPopulationType?: ProjectPopulationType;
  searchFilter: FilterData;
}): JSX.Element {
  const {
    cancelAdd,
    submitRow,
    bufferCategories,
    searchFilter,
    availableComponents,
    isRestricted = true,
    projectsPopulationType = ProjectPopulationType.NOT_APPLICABLE,
  } = props;
  const [portfolioRow, setPortfolioRow] = useState<PortfolioRow>(getEmptyPortfolioRow());
  const [availableVintages, setAvailableVintages] = useState<Vintage[]>();
  const { api } = useContext(AxiosContext);
  const filter = createFilterOptions();
  const [projectFilter, setProjectFilter] = useState<string>(INITIAL_SEARCH_PHRASE);
  const [availableProjects, setAailableProjects] = useState<Project[]>();

  const projectBalanceQuery: string = useMemo(() => {
    switch (projectsPopulationType) {
      case ProjectPopulationType.AVAILABLE:
        return "&hasBalance=true";
      case ProjectPopulationType.OTHER:
        return "&hasBalance=false";
      default:
        return "";
    }
  }, [projectsPopulationType]);

  const getAvailableVintages = useCallback(
    async (id: uuid): Promise<AdminProjectVintageResponse[]> => {
      const vintagesResponse = await api.get<AdminProjectVintageQueryResponse>(
        `admin/project-vintages?${generateQueryParams({
          limit: SERVER_PAGINATION_LIMIT,
          projectIds: id,
          includeRelations: [
            ProjectVintageRelations.PROJECT,
            ProjectVintageRelations.ASSET_ALLOCATIONS_BY_BOOK_TYPE,
            ProjectVintageRelations.PRICES,
          ],
        })}`,
      );

      return vintagesResponse.data.data;
    },
    [api],
  );

  const { data: projectsList } = useSWR<AdminProjectResponse[]>(
    projectFilter
      ? `/admin/projects/search?name=true&id=true&fuzzy=true&limit=50&q=${projectFilter}${projectBalanceQuery}${createQueryFromFilter(searchFilter)}`
      : null,
  );

  useEffect(() => {
    if (!!projectsList && projectsList.length > 0) {
      const result: Project[] = projectsList
        ?.map((p) => ({
          registryProjectId: p.registryProjectId,
          id: p.id,
          name: p.name,
          rctStandard: p.rctStandard,
          suspended: p.suspended,
          type: p.projectType,
          isScienceTeamApproved: p.isScienceTeamApproved,
          bufferCategory: p?.bufferCategory,
          country: p?.country,
          integrityGradeScore: p?.integrityGradeScore,
        }))
        .sort((a, b) => a?.registryProjectId?.localeCompare(b?.registryProjectId));

      setAailableProjects(result);
    } else {
      setAailableProjects([]);
    }
  }, [projectsList]);

  const cancelNewRowHandler = (): void => {
    setPortfolioRow(getEmptyPortfolioRow());
    setAvailableVintages(undefined);
    cancelAdd();
  };

  const onChangeHandler = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>): void => {
      const value = event.target.value;
      const name = event.target.name;
      const row = { ...portfolioRow };
      if (name === 'costBasis' || name === 'portfolioManagerEstimate') {
        (row as any)[name].value = value;
      }
      setPortfolioRow(row);
    },
    [portfolioRow],
  );

  const onBufferPercentageChangeHandler = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>): void => {
      const value = event.target.value;
      const row = { ...portfolioRow };
      if (!row.bufferPercentage) {
        row.bufferPercentage = { value: "" };
      }
      row.bufferPercentage.value = ((convertStringToNumber(value) ?? 0) / 100).toString();
      setPortfolioRow(row);
    },
    [portfolioRow],
  );

  const onBufferCategoryChangeHandler = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>, newValue: BufferCategory): void => {
      if (newValue) {
        setPortfolioRow({
          ...portfolioRow,
          bufferCategory: {
            value: {
              id: newValue.id,
              name: newValue.name,
            },
          },
        });
      } else {
        setPortfolioRow({
          ...portfolioRow,
          bufferCategory: {},
        });
      }
    },
    [portfolioRow],
  );

  const onQuantityChangeHandler = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>): void => {
      setPortfolioRow(handleQuantityChange(portfolioRow, event.target.value, projectsPopulationType));
    },
    [portfolioRow, projectsPopulationType],
  );

  const projectSelectionHandler = useCallback(
    async (event: React.ChangeEvent<HTMLInputElement>, newValue: Project): Promise<void> => {
      let selectedBufferCat: BufferCategoryField;
      const newPortfolioRow = { ...portfolioRow };
      if (!newValue) {
        setPortfolioRow(getEmptyPortfolioRow());
      } else {
        if (newValue && newValue.inputValue) {
          newPortfolioRow.registryProjectId = {
            value: {
              registryProjectId: newValue.registryProjectId,
            },
          };
          setPortfolioRow(newPortfolioRow);
          setAvailableVintages([]);
        } else {
          if (newValue?.name) newPortfolioRow.projectName = { value: newValue?.name };
          if (newValue?.type) newPortfolioRow.projectType = { value: newValue?.type.type };
          if (newValue?.integrityGradeScore)
            newPortfolioRow.integrityGradeScore = { value: newValue?.integrityGradeScore?.toString() };
          newPortfolioRow.registryProjectId = { value: newValue };
          newPortfolioRow.vintageInterval = { value: { name: "" } };
          newPortfolioRow.amountAllocated = { value: "0", message: "", error: false };
          newPortfolioRow.inventoryShortage = { value: "0", message: "", error: false };

          if (newValue?.bufferCategory) {
            selectedBufferCat = {
              value: {
                id: newValue.bufferCategory?.id,
                name: newValue.bufferCategory?.name,
              },
            };
            newPortfolioRow.bufferCategory = selectedBufferCat;
          } else {
            newPortfolioRow.bufferCategory = {};
          }
          setPortfolioRow(newPortfolioRow);
        }

        if (newValue?.id) {
          const vintages = await getAvailableVintages(newValue.id);
          if (vintages) {
            const sortedVintagesList = vintages.sort((a, b) => (+a.name < +b.name ? -1 : +a.name > +b.name ? 1 : 0));
            const responseTrader = await api.post<AdminVintagePricingResponse[]>(
              `reporting/vintage-pricing?source=rubicon_trader`,
              { vintage_ids: sortedVintagesList.map((x) => x.id) },
            );

            const result: Vintage[] = vintages.map((v) => ({
              id: v.id,
              name: v.name,
              amountAvailable: getAmountAvailable(v),
              holdingAmount: getHoldingAmount(v),
              averageCostBasis: v.averageCostBasis,
              riskBufferPercentage: v.riskBufferPercentage,
              latestTraderPrice: responseTrader.data.find((y) => y.vintage_id == v.id)?.price ?? undefined,
              bufferCategory: { id: selectedBufferCat?.value?.id, name: selectedBufferCat?.value?.name },
              assetAllocationsByBookType: v.assetAllocationsByBookType,
            }));

            //filter out vintages that were already selected
            setAvailableVintages(
              result?.filter((v) => !availableComponents?.some((av: any) => av.projectVintage?.id === v.id)),
            );
          }
        }
      }
    },
    [api, getAvailableVintages, portfolioRow, availableComponents],
  );

  const vintageSelectionHandler = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>, newValue: Vintage): void => {
      if (typeof newValue === "string") {
        const newVintageInterval: VintageFormField = {
          value: { name: newValue },
        };
        setPortfolioRow({
          ...portfolioRow,
          vintageInterval: newVintageInterval,
        });
      } else if (newValue && newValue.inputValue) {
        const newVintageInterval: VintageFormField = {
          value: { name: newValue.name },
        };

        //if a custom vintage was selected then clear related fields
        if (portfolioRow?.vintageInterval?.value?.id) {
          setPortfolioRow({
            ...portfolioRow,
            costBasis: { value: "0" },
            portfolioManagerEstimate: { value: "0" },
            bufferPercentage: { value: "0" },
            vintageInterval: newVintageInterval,
            amountAllocated: {
              value: portfolioRow.amountAllocated?.value,
              message: "",
              error: false,
            },
            inventoryShortage: {},
            bufferCategory: {},
          });
        } else {
          setPortfolioRow({
            ...portfolioRow,
            vintageInterval: newVintageInterval,
          });
        }
      } else {
        if (newValue) {
          setPortfolioRow({
            ...portfolioRow,
            amountAllocated: {
              value: portfolioRow.amountAllocated?.value,
              error: false,
              message: `${newValue?.holdingAmount ? `Holding: ${integerFormat(newValue.holdingAmount)}` : ""} ${newValue?.amountAvailable ? `Available: ${integerFormat(newValue.amountAvailable)}` : ""}`,
            },
            vintageInterval: { value: newValue },
            portfolioManagerEstimate: { value: (+(newValue.latestTraderPrice ?? 0)).toString() },
            costBasis: {
              value: newValue?.averageCostBasis
                ? newValue?.averageCostBasis.toFixed(4, Decimal.ROUND_HALF_UP).toString()
                : "",
            },
            assetAllocationsByBookType: newValue?.assetAllocationsByBookType,
            inventoryShortage: getInventoryShortage(
              convertStringToNumber(portfolioRow?.amountAllocated?.value ?? ""),
              newValue.amountAvailable,
              newValue?.assetAllocationsByBookType ?? [],
            ),
            amountAvailable: newValue.amountAvailable,
            bufferPercentage: {
              value: newValue?.riskBufferPercentage ? newValue?.riskBufferPercentage.toString() : "",
            },
            bufferCategory: {
              value: {
                id: newValue?.bufferCategory?.id,
                name: newValue?.bufferCategory?.name,
              },
            },
          });
        }
      }
    },
    [portfolioRow],
  );

  const projectCategoryDefaultProps = useMemo(
    () => ({
      options: bufferCategories ?? [],
      getOptionLabel: (option: AdminBufferCategoryResponse) => (option?.name ? option.name : ""),
      isOptionEqualToValue: (option: AdminBufferCategoryResponse, value: AdminBufferCategoryResponse) =>
        option.id === value.id,
    }),
    [bufferCategories],
  );

  const projectRegistryIdDefaultProps = useMemo(
    () => ({
      options: availableProjects ?? [],
      getOptionLabel: (option: Project): string => {
        return option?.registryProjectId ? `${option.registryProjectId} - ${option.name}` : "";
      },

      isOptionEqualToValue: (option: Project, value: Project): boolean => option.id === value.id,
    }),
    [availableProjects],
  );

  const vintagesDefaultProps = useMemo(
    () => ({
      options: availableVintages ?? [],
      getOptionLabel: (option: Vintage): string => {
        if (typeof option === "string") {
          return option;
        }

        if (option.inputValue) {
          return option.inputValue;
        }
        return option?.name;
      },
      isOptionEqualToValue: (option: Vintage, value: Vintage): boolean => option.id === value.id,
      filterOptions: (options: Vintage[], params: { inputValue: string }): unknown[] => {
        const filtered = filter(options, {
          inputValue: params.inputValue,
          getOptionLabel: (option: unknown): string => {
            if (typeof option === 'string') {
              return option;
            }
            const vintageOption = option as Vintage;
            if (vintageOption?.inputValue) {
              return vintageOption.inputValue;
            }
            return vintageOption?.name || '';
          },
        });
        if (params.inputValue !== "") {
          filtered.push({
            inputValue: `+ Add ${params.inputValue}`,
            name: params.inputValue,
          });
        }
        return filtered;
      },
    }),
    [availableVintages, filter],
  );

  const onSearchChangeHandler = (event: React.ChangeEvent<HTMLInputElement>): void => {
    if (isEmpty(event.target.value)) {
      setProjectFilter(INITIAL_SEARCH_PHRASE);
    } else {
      setProjectFilter(event.target.value);
    }
    setProjectFilter(event.target.value);
  };

  const onProjectBlur = (): void => {
    setProjectFilter(INITIAL_SEARCH_PHRASE);
  };

  const onClearProject = (): void => {
    setPortfolioRow(getEmptyPortfolioRow());
    setProjectFilter(INITIAL_SEARCH_PHRASE);
  };

  const newRowDef = useMemo<ColDef[]>(
    () => [
      {
        id: "rctEligible",
        title: "",
        type: "display",
        value: "rctEligible",
        formatter: {
          func: (): JSX.Element => <></>,
        },
      },
      {
        id: "registryProjectId",
        title: "Project",
        type: "autocomplete",
        value: portfolioRow?.registryProjectId?.value,
        onChange: projectSelectionHandler,
        helperText: portfolioRow?.registryProjectId?.message,
        error: portfolioRow?.registryProjectId?.error,
        defaultProps: projectRegistryIdDefaultProps,
        tooltip:
          isEmpty(portfolioRow?.registryProjectId?.value?.id) &&
          !isEmpty(portfolioRow?.registryProjectId?.value?.registryProjectId)
            ? "this is a custom project"
            : "",
        mode: "edit",
        class: "projectSelector",
        onSearchChange: onSearchChangeHandler,
        onBlur: onProjectBlur,
        onClear: onClearProject,
        placeHolder: "Search and select a project",
      },
      {
        id: "type",
        title: "Type",
        type: "display",
        value: portfolioRow?.projectType?.value,
      },
      {
        id: "integrityGradeScore",
        title: "Rubicon Carbon Integrity Grade",
        type: "display",
        value: portfolioRow?.integrityGradeScore?.value ?? MISSING_DATA,
        formatter: {
          func: (): JSX.Element => {
            const integrityGradeScore = availableProjects?.find(
              (p) => p.id === portfolioRow.registryProjectId?.value?.id,
            )?.integrityGradeScore;
            return (
              <>
                <Maybe condition={!!integrityGradeScore}>
                  <Box sx={{ marginTop: "-5px" }}>
                    <IntegrityScore
                      score={Math.round(integrityGradeScore ?? 0)}
                      separate
                      hideSubtext
                      className={classes.integrityGrade}
                    />
                  </Box>
                </Maybe>
                <Maybe condition={!integrityGradeScore}>
                  <Box>{MISSING_DATA}</Box>
                </Maybe>
              </>
            );
          },
        },
      },
      {
        id: "country",
        title: "Country",
        type: "display",
        value: portfolioRow?.registryProjectId?.value?.country?.name,
      },
      {
        id: "vintageInterval",
        title: "Vintage",
        type: "autocomplete",
        value: portfolioRow?.vintageInterval?.value,
        onChange: vintageSelectionHandler,
        helperText: portfolioRow?.vintageInterval?.message,
        error: portfolioRow?.vintageInterval?.error,
        defaultProps: vintagesDefaultProps,
        tooltip:
          isEmpty(portfolioRow?.vintageInterval?.value?.id) && !isEmpty(portfolioRow?.vintageInterval?.value?.name)
            ? "this is a custom vintage"
            : "",
        mode: "edit",
      },
      {
        id: "bufferPercentage",
        title: "Required Buffer",
        type: portfolioRow?.vintageInterval?.value?.id ? "display" : "number",
        value: portfolioRow?.bufferPercentage?.value,
        onChange: onBufferPercentageChangeHandler,
        helperText: portfolioRow?.bufferPercentage?.message,
        error: portfolioRow?.bufferPercentage?.error,
        formatter: { 
          func: (input: any): Nullable<string> => {
            if (typeof input === 'number') {
              return decimalFormat(input * 100);
            }
            const value = input instanceof Map ? input.get('bufferPercentage') : input;
            return decimalFormat(Number(value) * 100);
          },
        },
        mode: "edit",
        hidden: isRestricted,
        valueFormatter: percentageFormat,
        suffix: "%",
      },
      {
        id: "bufferCategory",
        title: "Buffer Category",
        type: portfolioRow?.vintageInterval?.value?.id ? "display" : "autocomplete",
        value: portfolioRow?.vintageInterval?.value?.id
          ? portfolioRow?.bufferCategory?.value?.name
          : portfolioRow?.bufferCategory?.value,
        defaultProps: projectCategoryDefaultProps as any,
        onChange: onBufferCategoryChangeHandler,
        helperText: portfolioRow?.bufferCategory?.message,
        error: portfolioRow?.bufferCategory?.error,
        mode:
          isEmpty(portfolioRow?.vintageInterval?.value?.id) && !isEmpty(portfolioRow?.vintageInterval?.value?.name)
            ? "edit"
            : "display",
        hidden: isRestricted,
      },
      {
        id: "amountAllocated",
        title: "Quantity",
        type: "number",
        value: portfolioRow?.amountAllocated?.value,
        onChange: onQuantityChangeHandler,
        helperText: portfolioRow?.amountAllocated?.message,
        error: portfolioRow?.amountAllocated?.error,
        formatter: { func: integerFormat as any },
        mode: "edit",
      },
      {
        id: "inventoryShortage",
        title: "Inventory Shortage",
        type: "display",
        value: MISSING_DATA,
        formatter: {
          func: (): JSX.Element => (
            <InventoryShortage
              projectsPopulationType={projectsPopulationType}
              availableAmount={portfolioRow?.amountAvailable ?? 0}
              portfolioRow={portfolioRow}
            />
          ),
        },
        mode: "edit",
        hidden: isRestricted,
      },
      {
        id: "costBasis",
        title: "Cost Basis",
        type: portfolioRow?.vintageInterval?.value?.id ? "display" : "number",
        value: portfolioRow?.costBasis?.value,
        onChange: onChangeHandler,
        helperText: portfolioRow?.costBasis?.message,
        error: portfolioRow?.costBasis?.error,
        decimalScale: 4,
        prefix: "$",
        mode: "edit",
        hidden: isRestricted,
      },
      {
        id: "portfolioManagerEstimate",
        title: "MTM",
        type: portfolioRow?.vintageInterval?.value?.id ? "display" : "number",
        value: portfolioRow?.portfolioManagerEstimate?.value,
        onChange: onChangeHandler,
        helperText: portfolioRow?.portfolioManagerEstimate?.message,
        error: portfolioRow?.portfolioManagerEstimate?.error,
        decimalScale: 2,
        prefix: "$",
        formatter: { func: currencyFormat as any },
        mode: "edit",
        hidden: isRestricted,
      },
    ],
    [
      portfolioRow,
      onChangeHandler,
      onBufferPercentageChangeHandler,
      onBufferCategoryChangeHandler,
      onQuantityChangeHandler,
      vintageSelectionHandler,
      projectSelectionHandler,
      isRestricted,
      projectRegistryIdDefaultProps,
      vintagesDefaultProps,
      projectCategoryDefaultProps,
      availableProjects,
      projectsPopulationType,
    ],
  );

  const submitRowHandler = (): void => {
    const validatedRow = validateRow(portfolioRow);
    setPortfolioRow(validatedRow);
    if (isValidRow(validatedRow)) {
      submitRow(validatedRow, isRestricted);
    }
  };

  return (
    <React.Fragment>
      <TableRow sx={{ verticalAlign: "top" }}>
        <BaseRow rowDef={newRowDef} type={"new"} />
        <TableCell>
          <Stack direction="row" gap={2}>
            <IconButton
              sx={{ color: "rgba(154, 198, 106, 1)" }}
              edge="start"
              onClick={() => submitRowHandler()}
            >
              <CheckIcon />
            </IconButton>
            <IconButton sx={{ color: "rgba(0, 0, 0, 0.56)" }} edge="start" onClick={cancelNewRowHandler}>
              <DeleteIcon />
            </IconButton>
          </Stack>
        </TableCell>
      </TableRow>
    </React.Fragment>
  );
}
