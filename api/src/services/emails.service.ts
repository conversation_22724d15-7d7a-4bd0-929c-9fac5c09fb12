/* third party */
import { RedisService } from '@liaoliaots/nestjs-redis';
import { EntityManager } from '@mikro-orm/postgresql';
import { BadRequestException, ConflictException, Injectable, UnprocessableEntityException } from '@nestjs/common';
import { MailDataRequired } from '@sendgrid/mail';
import { isDefined } from 'class-validator';
import Decimal from 'decimal.js';
import Redis from 'ioredis';
/* rubicon */
import { AuditLogAction, BookType, uuid } from '@rubiconcarbon/shared-types';
/* env */
import { environment } from '@env/environment';
/* app */
import { UserClaims } from '@app/auth/interfaces';
import {
  PortalByorctEmailRequestDTO,
  PortalByorctEmailResponseDTO,
  PortalPurchaseEmailRequestDTO,
} from '@app/dtos/email.dto';
import { ByorctConfirmation, ByorctProjectDetails, PurchaseConfirmation } from '@app/dtos/sendgrid-payload.dto';
import { Book, ProjectVintage } from '@app/entities';
import { getPortalUserWithOrganizationFromClaims } from '@app/helpers';
import { InternalPortalUser } from '@app/interfaces/user.interface';
import { AuditLogsService } from '@app/utility/audit-log';
import { ModelPortfoliosService } from './model-portfolios.service';
import { SendgridService } from './sendgrid.service';
import { InternalCachedBYOQuote } from '@app/interfaces/model-portfolio.interface';

@Injectable()
export class EmailsService {
  private redis: Redis;

  constructor(
    private readonly auditLogsService: AuditLogsService,
    private readonly em: EntityManager,
    private readonly modelPortfoliosService: ModelPortfoliosService,
    private readonly redisService: RedisService,
    private readonly sendgrid: SendgridService,
  ) {
    this.redis = this.redisService.getClient();
  }

  /**
   * send BYORCT email and create model portfolio for requested vintages
   *
   * steps:
   * 1. check if data.cachedEstimateId exists, throw UnprocessableEntityException if missing
   * 2. get cached data from Redis using cachedEstimateId, throw UnprocessableEntityException if not found
   * 3. parse cached data as CachedBYOQuoteDTO and calculate priceEstimate
   * 4. start a database transaction
   * 5. get InternalPortalUser using getPortalUserWithOrganizationFromClaims and populate rubiconManager
   * 6. check if CustomerPortfolio exists, throw UnprocessableEntityException if not
   * 7. get list of ProjectVintage ids from cached components and fetch ProjectVintage entities
   * 8. if number of vintages does not match components, throw UnprocessableEntityException
   * 9. calculate total tonnes and create ByorctProjectDetails for each vintage
   * 10. create model portfolio using ModelPortfoliosService
   * 11. update model portfolio components using ModelPortfoliosService
   * 12. prepare ByorctConfirmation payload for email
   * 13. create email template for user and internal recipients
   * 14. send emails using SendgridService
   * 15. delete cached estimate from Redis
   * 16. create AuditLogsService entry for BYORCT_REQUESTED
   * 17. flush transaction
   * 18. return ByorctEmailResponseDTO with modelPortfolioId and rubiconManager
   *
   * @param {UserClaims} claims
   * @param {PortalByorctEmailRequestDTO} data
   * @returns {Promise<PortalByorctEmailResponseDTO>}
   * @throws UnprocessableEntityException, BadRequestException
   */
  async sendByorctEmail(claims: UserClaims, data: PortalByorctEmailRequestDTO): Promise<PortalByorctEmailResponseDTO> {
    const now = new Date();

    // cached data is called from the pricing endpoint /byorct/pricing
    // it gets cached in redis and then can be called here
    // note : there is an email that is also sent there
    if (!data.cachedEstimateId) {
      throw new UnprocessableEntityException(`Missing cachedEstimateId`);
    }
    const cachedData = await this.redis.get(`BYO-${data.cachedEstimateId}`);
    if (!cachedData) {
      throw new UnprocessableEntityException(`BYO estimate not found`);
    }
    const cached: InternalCachedBYOQuote = JSON.parse(cachedData);
    const priceEstimate = (cached?.priceEstimate && new Decimal(cached.priceEstimate)) || new Decimal(0);

    // todo (TD-83) : this needs to be cleaned up with emails
    return await this.em.transactional(async (tx) => {
      const user: InternalPortalUser = await getPortalUserWithOrganizationFromClaims(tx, claims);
      await tx.populate(user, ['organizationUser.organization.customerPortfolio.rubiconManager']);

      if (!user.organizationUser.organization.customerPortfolio) {
        throw new UnprocessableEntityException(`CustomerPortfolio must exist to send email`);
      }

      // get list of vintages
      const vintageIds: uuid[] = cached.components.map((c) => c.vintageId).filter(isDefined);
      const vintages = await tx.find(ProjectVintage, { id: vintageIds }, { populate: ['project.projectType'] });
      if (vintages.length !== cached.components.length) {
        throw new UnprocessableEntityException(
          `expected ${cached.components.length} ProjectVintages but found ${vintages.length}`,
        );
      }

      // calculate amounts and create new ByorctProjectDetails for each vintage
      const calculatedTotalTonnes = cached.components.reduce((p, c) => p + c.amountAllocated, 0);
      const emailProjectFields = cached.components
        .filter((v) => v.amountAllocated > 0)
        .map((f) => {
          const vintage = vintages.find((pv) => f.vintageId === pv.id);
          if (!vintage) {
            throw new BadRequestException([`vintageId ${f.vintageId} must be a valid id`]);
          }

          return new ByorctProjectDetails(
            vintage.project.id,
            vintage.project.name,
            f.amountAllocated,
            calculatedTotalTonnes,
            vintage.name(),
          );
        });

      // create model portfolio to be saved in the db
      // this gives admin users future access to data requested by customer
      const modelPortfolio = await this.modelPortfoliosService.createBYOModelPortfolio(
        tx,
        claims,
        now,
        cached && cached.includeRiskAdjustment,
        data.modelPortfolioName,
        priceEstimate,
      );
      data.modelPortfolioName = modelPortfolio.name;

      await this.modelPortfoliosService.updateModelPortfolioComponents(tx, now, user, modelPortfolio, {
        create:
          cached &&
          cached.components
            .filter((mpc) => mpc.amountAllocated > 0)
            .map((mpc) => {
              const vintage = vintages.find((pv: ProjectVintage) => mpc.vintageId === pv.id);
              mpc.isBufferComponent = false;
              mpc.projectId = mpc.projectId || vintage?.project?.id;
              mpc.registryProjectId = mpc.registryProjectId || vintage?.project?.registryProjectId || '';
              return mpc;
            }),
        type: 'create',
      });

      // prepare byorct data to be sent as email
      const sendgridPayload = new ByorctConfirmation(
        user.organizationUser,
        cached.isPurchaseToRetire,
        cached.includeRiskAdjustment,
        emailProjectFields,
        priceEstimate,
        calculatedTotalTonnes,
      );

      // send email to user
      const emailTemplate: MailDataRequired = this.sendgrid.createEmailTemplate(
        user.email,
        environment.sendgrid.sender.alerts,
        environment.sendgrid.template.byorct,
        sendgridPayload,
      );

      // send email to internal mailing list
      const internalRecipients = [
        environment.sendgrid.receiver.byorct,
        user.organizationUser.organization.customerPortfolio.rubiconManager.email,
      ];
      if (environment.sendgrid.receiver.asana) {
        internalRecipients.push(environment.sendgrid.receiver.asana);
      }
      const internalTemplate: MailDataRequired = this.sendgrid.createEmailTemplate(
        internalRecipients,
        environment.sendgrid.sender.alerts,
        environment.sendgrid.template.notifications.realtime.byorct,
        sendgridPayload,
      );
      await this.sendgrid.sendEmails([emailTemplate, internalTemplate]);
      await this.redis.del(`BYO-${data.cachedEstimateId}`);

      await this.auditLogsService.create(tx, claims, AuditLogAction.BYORCT_REQUESTED, now, user.id, {
        user: claims.email,
        org: user.organizationUser.organization.name,
        ...cached,
        ...data,
      });

      await tx.flush();
      return {
        modelPortfolioId: modelPortfolio.id,
        rubiconManager: user.organizationUser.organization.customerPortfolio.rubiconManager,
      };
    });
  }

  /**
   * send purchase confirmation email to user and internal recipients
   *
   * steps:
   * 1. get InternalPortalUser from UserClaims
   * 2. find Book entity by rctId and type or organization
   * 3. if Book not found, throw BadRequestException
   * 4. determine perTonneAmount based on isRiskAdjusted
   * 5. if perTonneAmount not found, throw ConflictException
   * 6. calculate totalPrice
   * 7. create PurchaseConfirmation payload
   * 8. create email template for user
   * 9. create email template for internal recipients
   * 10. send emails using SendgridService
   * 11. create AuditLogsService entry for DEPRECATED_PURCHASE_REQUESTED
   * 12. flush EntityManager
   *
   * @param {UserClaims} claims
   * @param {PortalPurchaseEmailRequestDTO} data
   * @returns {Promise<void>}
   * @throws BadRequestException, ConflictException
   */
  async sendPurchasesEmail(claims: UserClaims, data: PortalPurchaseEmailRequestDTO): Promise<void> {
    const portalUser: InternalPortalUser = await getPortalUserWithOrganizationFromClaims(this.em, claims);

    // todo : (TD-87) add assets organizations table and then can just check relation
    const rctBook = await this.em.findOne(
      Book,
      {
        $or: [
          { id: data.rctId, type: BookType.PORTFOLIO_PUBLIC },
          { id: data.rctId, organization: portalUser.organizationUser.organization },
        ],
      },
      { populate: ['organization'] },
    );
    if (!rctBook) {
      throw new BadRequestException(`Book ${data.rctId} not found`);
    }

    const perTonneAmount = data.isRiskAdjusted ? rctBook.purchasePriceWithBuffer : rctBook.purchasePrice;
    if (!perTonneAmount) {
      throw new ConflictException(`Book ${data.rctId} cannot be purchased`);
    }
    const totalPrice = perTonneAmount.mul(data.amount);

    const sendgridPayload = new PurchaseConfirmation(
      portalUser.organizationUser,
      data.isPurchaseToRetire,
      data.isRiskAdjusted,
      rctBook,
      perTonneAmount,
      totalPrice,
      data.amount,
    );

    // send email to user
    const emailTemplate: MailDataRequired = this.sendgrid.createEmailTemplate(
      portalUser.email,
      environment.sendgrid.sender.alerts,
      environment.sendgrid.template.purchaseRequested,
      sendgridPayload,
    );

    // send email to internal mailing list
    const internalTemplate: MailDataRequired = this.sendgrid.createEmailTemplate(
      [
        environment.sendgrid.receiver.purchase,
        portalUser.organizationUser.organization.customerPortfolio?.rubiconManager.email || '',
      ],
      environment.sendgrid.sender.alerts,
      environment.sendgrid.template.notifications.realtime.purchaseRequested,
      sendgridPayload,
    );
    await this.sendgrid.sendEmails([emailTemplate, internalTemplate]);

    await this.auditLogsService.create(
      this.em,
      claims,
      AuditLogAction.DEPRECATED_PURCHASE_REQUESTED,
      new Date(),
      portalUser.id,
      {
        user: claims.email,
        org: portalUser.organizationUser.organization.name,
        ...data,
      },
    );
    await this.em.flush();
  }
}
