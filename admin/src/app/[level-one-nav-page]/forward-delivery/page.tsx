import { AuthorizeServer } from "@app/authorize-server";
import {
  AdminForwardQueryResponse,
  PermissionEnum,
  BookRelations,
  AdminOrganizationQueryResponse,
  AdminGroupingParentQueryResponse,
} from "@rubiconcarbon/shared-types";
import { withErrorHandling } from "@app/data-server";
import { baseApiRequest, generateQueryParams } from "@app/libs/server";
import { isValidElement, type JSX } from "react";
import ForwardDeliveryPage from "./components";
import { SERVER_PAGINATION_LIMIT } from "@constants/constants";

/**
 * Forward Delivery Page
 *
 * This is a server component that renders the Forward Delivery page
 */
export default async function ForwardDelivery({
  searchParams,
}: {
  searchParams: Promise<{ type?: "buy" | "sell"; search?: string }>;
}): Promise<JSX.Element> {
  const { type, search } = await searchParams;

  const forwardsResponse = await withErrorHandling(async () =>
    baseApiRequest<AdminForwardQueryResponse>(
      `admin/forwards?${generateQueryParams({
        includeTotalCount: true,
        limit: SERVER_PAGINATION_LIMIT,
        ...(type ? { type } : {}),
      })}`,
    ),
  );

  const organizationsResponse = await withErrorHandling(async () =>
    baseApiRequest<AdminOrganizationQueryResponse>(
      `admin/organizations?${generateQueryParams({
        limit: SERVER_PAGINATION_LIMIT,
        isEnabled: true,
      })}`,
    ),
  );

  const booksByParentResponse = await withErrorHandling(async () =>
    baseApiRequest<AdminGroupingParentQueryResponse>(
      `admin/books/parents?${generateQueryParams({
        limit: SERVER_PAGINATION_LIMIT,
        isEnabled: true,
        includeTotalCount: true,
        includeRelations: [
          BookRelations.OWNER_ALLOCATIONS,
          BookRelations.OWNER_ALLOCATIONS_NESTED,
          BookRelations.OWNER_ALLOCATIONS_BY_ASSET_TYPE,
          BookRelations.PRICES,
        ],
      })}`,
    ),
  );

  // Check if any of the results is a server error
  if (isValidElement(forwardsResponse)) return forwardsResponse;
  if (isValidElement(organizationsResponse)) return organizationsResponse;
  if (isValidElement(booksByParentResponse)) return booksByParentResponse;

  return (
    <AuthorizeServer permissions={[PermissionEnum.FORWARDS_READ]}>
      <ForwardDeliveryPage
        forwardsResponse={forwardsResponse as AdminForwardQueryResponse}
        organizationsResponse={organizationsResponse as AdminOrganizationQueryResponse}
        booksByParentResponse={booksByParentResponse as AdminGroupingParentQueryResponse}
        type={type as "buy" | "sell"}
        search={search}
      />
    </AuthorizeServer>
  );
}
