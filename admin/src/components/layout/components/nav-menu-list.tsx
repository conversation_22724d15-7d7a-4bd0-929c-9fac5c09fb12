import useNavigationMenu from "@providers/navigation-menu/navigation-menu-provider";
import { List } from "@mui/material";
import NavMenuItem from "./nav-menu-item";
import { classcat } from "@rubiconcarbon/frontend-shared";
import { useStoreProvider } from "@providers/store-provider";

import type { JSX } from "react";

import classes from "../styles/nav-menu.module.scss";

type NavMenuListProps = {
  type: "sidebar" | "mobile";
  onMenuItemClick?: () => void;
};

const NavMenuList = ({ type, onMenuItemClick }: NavMenuListProps): JSX.Element => {
  const { permissibleMenus } = useNavigationMenu();
  const { localState } = useStoreProvider();
  const { navigation } = localState;
  const { expanded = false, pinned = false } = navigation || {};

  return (
    <List
      className={classcat([
        classes.NavMenuList,
        { [classes.SideBarOverride]: type === "sidebar", [classes.SideBarOverrideExpanded]: expanded || pinned },
      ])}
    >
      {permissibleMenus?.map((menu) => <NavMenuItem key={menu.link} menu={menu} onMenuClick={onMenuItemClick} />)}
    </List>
  );
};

export default NavMenuList;
