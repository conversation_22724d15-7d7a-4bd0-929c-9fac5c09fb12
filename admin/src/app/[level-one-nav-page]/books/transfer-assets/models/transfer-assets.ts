import { AdminBookResponse, AdminAllocationResponse, BookType, uuid } from "@rubiconcarbon/shared-types";
import { Transform, Type } from "class-transformer";
import { Equals, IsNotEmpty, Min, ValidateIf, ValidateNested } from "class-validator";
import { isNothing, MaybeNothing, Nullable } from "@rubiconcarbon/frontend-shared";
import { NoNumberFormat } from "@utils/formatters/no-number-format";

export class TransferAssetItem {
  @IsNotEmpty({ message: "Required." })
  sourceId: uuid;

  source: Nullable<AdminBookResponse>;

  @IsNotEmpty({ message: "Required." })
  @ValidateIf((o: TransferAssetItem) => !!o?.sourceId)
  destinationId: uuid;

  destination: Nullable<AdminBookResponse>;

  @IsNotEmpty({ message: "Required." })
  @ValidateIf((o: TransferAssetItem) => !!o?.sourceId)
  assetId: uuid;

  asset: Nullable<AdminAllocationResponse>; // todo : @kofi what is this supposed to be? it supposedly mapped to AllocationResponse but it could jsut be TrimmedAssetResponse or it could be AssetTransferResponse?

  @Min(1, { message: "Amount must be greater than 0." })
  @Transform(({ value }: { value: MaybeNothing<string> }) =>
    !isNothing(value) && value !== "" ? Number(NoNumberFormat(value!)) : "",
  )
  @IsNotEmpty({ message: "Required." })
  @ValidateIf((o: TransferAssetItem) => !!o?.assetId)
  amount: string;

  value: string; // this is a derived field and it is readonly

  @Equals(true)
  @ValidateIf(
    (o: TransferAssetItem) =>
      BookType.COMPLIANCE_DEFAULT === o?.source?.type || BookType.COMPLIANCE_DEFAULT === o?.destination?.type,
  )
  complainceApproved: boolean;

  @Equals(true)
  @ValidateIf((o: TransferAssetItem) => BookType.REHABILITATION_DEFAULT === o?.destination?.type)
  rehabApproved: boolean;
}

export class TransferAssetsModel {
  @ValidateNested()
  @Type(() => TransferAssetItem)
  assets: TransferAssetItem[];

  memo: string;
}
