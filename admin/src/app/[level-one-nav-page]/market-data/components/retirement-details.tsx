import { Box, Stack } from "@mui/material";
import { SupersetDashboard } from "@components/superset-dashboard/superset-dashboard";

import type { JSX } from "react";

import classes from "../styles/styles.module.scss";

const ExploreRetirements = (): JSX.Element => {
  const DASHBOARD_KEY = "52fa7eed-2b7a-41ab-b4b9-7014423aaa7c";

  return (
    <Box className={classes.boxContainer}>
      <Stack direction="row" sx={{ justifyContent: "space-between" }}>
        <Box mt={1} className={classes.exploreTitle}>{`Retirement Transaction Details`}</Box>
      </Stack>
      <SupersetDashboard
        key={DASHBOARD_KEY}
        dashboard={DASHBOARD_KEY}
        config={{
          hideTitle: true,
          hideTab: true,
          hideChartControls: false,
          filters: { visible: false, expanded: false },
        }}
        style={{ height: "1150px", marginTop: "10px" }}
      />
    </Box>
  );
};

export default ExploreRetirements;
