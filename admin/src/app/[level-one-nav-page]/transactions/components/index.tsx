"use client";
import { AdminTransactionQueryResponse, AdminPurchaseQueryResponse, AdminTradeQueryResponse } from "@rubiconcarbon/shared-types";
import TransactionsComponent from "./transactions";

import type { JSX } from "react";

export default function Transactions({
  transactionsResponse,
  purchasesResponse,
  tradesResponse,
}: {
  transactionsResponse: AdminTransactionQueryResponse;
  purchasesResponse: AdminPurchaseQueryResponse;
  tradesResponse: AdminTradeQueryResponse;
}): JSX.Element {
  return (
    <TransactionsComponent
      transactionsResponse={transactionsResponse}
      purchasesResponse={purchasesResponse}
      tradesResponse={tradesResponse}
    />
  );
}
