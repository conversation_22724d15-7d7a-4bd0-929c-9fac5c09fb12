import React, { useContext, type JSX } from "react";
import { Typography } from "@mui/material";
import Button from "@mui/material/Button";
import Dialog from "@mui/material/Dialog";
import DialogActions from "@mui/material/DialogActions";
import DialogContent from "@mui/material/DialogContent";
import DialogTitle from "@mui/material/DialogTitle";
import { useState } from "react";
import { AxiosContext } from "@providers/axios-provider";
import { isEmpty } from "lodash";
import { AdminUserResponse, AdminUserCreateRequest } from "@rubiconcarbon/shared-types";
import COLORS from "@components/ui/theme/colors";
import { BaseDialogProps, DialogBackdrop } from "@models/dialogs";
import { HttpStatusLabels } from "@/constants/http";

const buttonStyle = {
  fontSize: "12px",
  fontWeight: 600,
  px: 3.5,
};
const dangerTheme = { backgroundColor: COLORS.red, color: COLORS.white };

const cancelButtonStyle = {
  ...buttonStyle,
  ...{ color: "#121212" },
};

interface InviteConfirmationModalProps extends BaseDialogProps {
  userInvite: AdminUserCreateRequest;
  organizationName: string;
}

export default function InviteConfirmationModal({
  isOpen,
  userInvite,
  organizationName,
  onClose,
  onConfirm,
  onError,
}: InviteConfirmationModalProps): JSX.Element | null {
  const [requestInFlight, setRequestInFlight] = useState(false);
  const { api } = useContext(AxiosContext);

  const submitHandler = (): void => {
    setRequestInFlight(true);
    api
      .post<AdminUserResponse>(`admin/users`, userInvite)
      .then(() => {
        onConfirm?.(`User ${userInvite.email} successfully created!`);
        onClose({ result: HttpStatusLabels.SUCCESS });
      })
      .catch((e) => {
        let message = `Failed to invite user ${userInvite.email}. `;
        if (!isEmpty(e?.response?.data?.message)) {
          message += e?.response?.data?.message;
        }
        console.error(message, e);
        onError?.(message);
        onClose({ result: HttpStatusLabels.ERROR });
      })
      .finally(() => {
        setRequestInFlight(false);
      });
  };

  return (
    <Dialog open={isOpen} onClose={onClose} maxWidth={"lg"}>
      <DialogTitle sx={dangerTheme}>Are you sure you want to create this user?</DialogTitle>
      <DialogContent sx={{ marginTop: "-20px" }}>
        <Typography variant="body1" component="p" sx={{ fontWeight: 500 }}>
          Are you sure you want to create user <b>{userInvite.email}</b> <br />
          as <b>{userInvite.organizationUserRoles}</b> in organization <b>{organizationName}</b>?
        </Typography>
        <DialogBackdrop requestInFlight={requestInFlight} />
      </DialogContent>
      <DialogActions>
        <Button disabled={requestInFlight} variant="text" onClick={onClose} sx={cancelButtonStyle}>
          Cancel
        </Button>
        <Button
          disabled={requestInFlight}
          variant="contained"
          type="submit"
          sx={buttonStyle}
          color="error"
          onClick={submitHandler}
        >
          Confirm
        </Button>
      </DialogActions>
    </Dialog>
  );
}
