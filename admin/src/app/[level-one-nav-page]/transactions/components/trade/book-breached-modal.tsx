import GenericDialog from "@components/ui/generic-dialog/generic-dialog";
import { BookTypeToLabel } from "@constants/book-type-to-label";
import { Stack, Typography } from "@mui/material";
import { BookType } from "@rubiconcarbon/shared-types";

import type { JSX } from "react";

import dialogClasses from "../../styles/dialog.module.scss";

type BookBreachedModalProps = {
  open: boolean;
  bookType: BookType;
  onContinue: () => void;
  onClose: () => void;
};

const BookBreachedModal = ({ open, bookType, onContinue, onClose }: BookBreachedModalProps): JSX.Element => (
  <GenericDialog
    open={open}
    title="Exceeding Portfolio Limits"
    positiveAction={{
      buttonText: "YES, CONTINUE ANYWAY",
      className: dialogClasses.InterruptPositiveAction,
    }}
    negativeAction={{
      buttonText: "CANCEL",
    }}
    onClose={onClose}
    onPositiveClick={onContinue}
    onNegativeClick={onClose}
    classes={{
      title: `${dialogClasses.Title} ${dialogClasses.InterruptTitle}`,
      actions: dialogClasses.Actions,
    }}
  >
    <Stack component="p" rowGap={1}>
      <Typography component="span">
        This trade will <strong>exceed</strong> the portfolio limits. By proceeding, the {BookTypeToLabel[bookType]}{" "}
        book will surpass its approved dollar amount.
      </Typography>
      <Typography component="span">Do you still wish to continue?</Typography>
    </Stack>
  </GenericDialog>
);

export default BookBreachedModal;
