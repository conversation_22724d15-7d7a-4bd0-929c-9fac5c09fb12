import React, { useState, useEffect, useContext, useMemo, type JSX } from "react";
import useS<PERSON> from "swr";
import { Box } from "@mui/material";
import {
  uuid,
  AdminModelPortfolioResponse,
  AdminModelPortfolioComponentResponse,
  AdminProjectQueryResponse,
  AdminProjectVintageQueryResponse,
  AdminBufferCategoryResponse,
  PermissionEnum,
  ProjectRelations,
  ProjectVintageRelations,
} from "@rubiconcarbon/shared-types";
import { AxiosContext } from "@providers/axios-provider";
import { isEmpty } from "lodash";
import { Maybe, pickFromArrayOfRecords } from "@rubiconcarbon/frontend-shared";
import {
  Project,
  PortfolioComponentResponse,
  getAmountAvailable,
  ProjectPopulationType,
  fillMissingProjectInfo,
  getHoldingAmount,
  getShortageAmount,
} from "./portfolio-sandbox-model";
import PortfolioSummary from "./portfolio-sandbox-summary";
import PortfolioSandboxMemo from "../portfolio-sandbox-memo/portfolio-sandbox-memo";
import PortfolioSandboxTable from "./portfolio-sandbox-table";
import { generateQueryParams } from "@rubiconcarbon/frontend-shared";
import { SERVER_PAGINATION_LIMIT } from "@constants/constants";
import useAuth from "@providers/auth-provider";
import LinkedPortfolios from "../linked-portfolios/linked-portfolios";
import PortfolioSandboxHeader from "@app/[level-one-nav-page]/portfolio-sandbox/components/portfolio-sandbox-header/portfolio-sandbox-header";

export default function PortfolioSandboxComponents(props: {
  mockPortfolio: AdminModelPortfolioResponse;
  refreshPortfolio: () => Promise<AdminModelPortfolioResponse>;
}): JSX.Element {
  const { mockPortfolio, refreshPortfolio } = props;
  const { api, apiFetcher } = useContext(AxiosContext);
  const { user: loginUser } = useAuth();

  const [portfolioComponents, setPortfolioComponents] = useState<AdminModelPortfolioComponentResponse[]>([]);
  const [availableProjects, setAvailableProjects] = useState<Project[]>([]);
  const [availableComponents, setAvailableComponents] = useState<PortfolioComponentResponse[]>([]);
  const [allProjectsComponents, setAllProjectsComponents] = useState<PortfolioComponentResponse[]>([]);
  const [availableProjectsComponents, setAvailableProjectsComponents] = useState<PortfolioComponentResponse[]>([]);

  const isRestricted: boolean = useMemo(
    () => !loginUser?.hasPermission(PermissionEnum.MODEL_PORTFOLIOS_ADVANCED_VIEW),
    [loginUser],
  );

  // this flag exists in order to indicate if Sales Order should be allowed
  const isAmountAvailableShortage: boolean = useMemo(
    () =>
      !!availableComponents
        ? availableComponents.some((c) => c?.amountAvailableShotage < 0 || !(c as any)?.["projectVintage"]?.["id"])
        : false,
    [availableComponents],
  );

  useEffect(() => {
    if (!!mockPortfolio && !isEmpty(mockPortfolio?.modelPortfolioComponents)) {
      setPortfolioComponents(mockPortfolio.modelPortfolioComponents!);
    } else {
      setPortfolioComponents([]);
    }
  }, [mockPortfolio]);

  const { data: bufferCategories = [] } = useSWR<AdminBufferCategoryResponse[]>(`/admin/buffer-categories`, {
    fetcher: apiFetcher as any,
    revalidateOnFocus: false,
    revalidateOnReconnect: false,
  });

  const projectIds: uuid[] = useMemo(
    () => (availableComponents?.length > 0 ? Array.from(new Set(availableComponents.map((c) => (c as any)?.["project"]?.["id"]))) : []),
    [availableComponents],
  );

  useEffect(() => {
    if (!isEmpty(portfolioComponents)) {
      const newVintageIds: string[] =
        portfolioComponents.length > 0
          ? pickFromArrayOfRecords<AdminModelPortfolioComponentResponse>(
              portfolioComponents,
             [ "projectVintage"],
            )
              .map((element: any) => element?.["projectVintage"]?.["id"])
              .filter((c) => !!c)
              .reduce((acc, curr) => (acc.includes(curr) ? acc : [...acc, curr]), [])
          : [];
      if (!!newVintageIds && newVintageIds.length > 0) {
        api
          .get<AdminProjectVintageQueryResponse>(
            `/admin/project-vintages?${generateQueryParams({
              ids: newVintageIds.join(","),
              limit: SERVER_PAGINATION_LIMIT,
              includeRelations: [
                ProjectVintageRelations.ASSET_ALLOCATIONS_BY_BOOK_TYPE,
                ProjectVintageRelations.PRICES,
              ],
            })}`,
          )
          .then((response) => {
            const vintagesComponents = response.data.data;
            const portfolioComponentResponse: PortfolioComponentResponse[] = portfolioComponents;
            if (!!vintagesComponents && vintagesComponents.length > 0) {
              const updatedComponents = portfolioComponentResponse.map((c: any) => {
                if (!!c?.["projectVintage"]?.["id"]) {
                  const vintageResponse = vintagesComponents.find((v) => v.id === c.projectVintage.id);
                  if (!!vintageResponse) {
                    const amountAvailable = getAmountAvailable(vintageResponse);
                    const holdingAmount = getHoldingAmount(vintageResponse);
                    c.costBasis = vintageResponse?.averageCostBasis;
                    c.bufferPercentage = vintageResponse?.riskBufferPercentage;
                    c.assetAllocationsByBookType = vintageResponse?.assetAllocationsByBookType;
                    //inventory shortage is calculated based on holding amount
                    c.inventoryShortage = +getShortageAmount(c.amountAllocated, holdingAmount);
                    c.amountAvailableShotage = +getShortageAmount(c.amountAllocated, amountAvailable);
                    c.amountAvailable = amountAvailable;
                    c.holdingAmount = holdingAmount;
                    c.vintageInterval = vintageResponse.name;
                    c.portfolioManagerEstimate = vintageResponse.price;
                  }
                }
                return c;
              });

              setAvailableComponents(fillMissingProjectInfo(availableProjects, updatedComponents));
            }
          });
      }
      setAvailableComponents(fillMissingProjectInfo(availableProjects, portfolioComponents));
    } else {
      setAvailableComponents([]);
    }
  }, [portfolioComponents, availableProjects, api]);

  const { data: projects } = useSWR<AdminProjectQueryResponse>(
    !isEmpty(projectIds)
      ? `/admin/projects?${generateQueryParams({
          limit: SERVER_PAGINATION_LIMIT,
          includeRelations: [ProjectRelations.COUNTRY, ProjectRelations.ASSET_ALLOCATIONS],
          ids: projectIds,
        })}`
      : null,
    {
      fetcher: apiFetcher as any,
      revalidateOnFocus: false,
      revalidateOnReconnect: false,
    },
  );

  useEffect(() => {
    if (projects?.data && (projects.data.length > 0)) {
      const result: Project[] = projects.data
        ?.map((p) => ({
          registryProjectId: p.registryProjectId,
          id: p.id,
          name: p.name,
          rctStandard: p.rctStandard,
          suspended: p.suspended,
          type: p.projectType,
          isScienceTeamApproved: p.isScienceTeamApproved,
          country: p?.country,
          integrityGradeScore: p?.integrityGradeScore,
        }))
        .sort((a, b) => a?.registryProjectId?.localeCompare(b?.registryProjectId));
      setAvailableProjects(result);
    }
  }, [projects]);

  useEffect(() => {
    const available: PortfolioComponentResponse[] = [];
    const all: PortfolioComponentResponse[] = [];

    if (!isEmpty(availableComponents)) {
      availableComponents?.forEach((element: any) => {
        if (element?.["project"]?.["hasBalance"]) {
          available.push(element);
        } else {
          all.push(element);
        }
      });
    }
    setAvailableProjectsComponents(available);
    setAllProjectsComponents(all);
  }, [projects, availableComponents, isRestricted]);

  return (
    <>
      <Box>
        <Box sx={{ display: "flex", justifyContent: "space-between" }}>
          <PortfolioSandboxHeader
            mockPortfolio={mockPortfolio}
            refreshPortfolio={refreshPortfolio}
            isRestricted={isRestricted}
            isAmountAvailableShortage={isAmountAvailableShortage}
          />
        </Box>
        <Maybe condition={!!bufferCategories}>
          <Box>
            <PortfolioSandboxTable
              mockPortfolio={mockPortfolio}
              hasEstimate={!!mockPortfolio?.priceEstimate}
              isDisabled={mockPortfolio?.showCustomer ?? true}
              availableComponents={availableProjectsComponents}
              availableProjects={availableProjects}
              bufferCategories={bufferCategories}
              refreshPortfolio={refreshPortfolio}
              isRestricted={isRestricted}
              projectsPopulationType={ProjectPopulationType.AVAILABLE}
            />
          </Box>
          <Box mt={4}>
            <PortfolioSandboxTable
              mockPortfolio={mockPortfolio}
              hasEstimate={!!mockPortfolio?.priceEstimate}
              isDisabled={mockPortfolio?.showCustomer ?? true}
              availableComponents={allProjectsComponents}
              availableProjects={availableProjects}
              bufferCategories={bufferCategories}
              refreshPortfolio={refreshPortfolio}
              isRestricted={isRestricted}
              projectsPopulationType={ProjectPopulationType.OTHER}
            />
          </Box>

          <Box mt={4}>
            <LinkedPortfolios portfolioId={mockPortfolio?.id} />
          </Box>
        </Maybe>
      </Box>
      <Maybe condition={!!bufferCategories && !isRestricted}>
        <Box mt={4}>
          <PortfolioSummary availableComponents={portfolioComponents} />
        </Box>
      </Maybe>
      <Box mt={4} mb={2}>
        <PortfolioSandboxMemo mockPortfolio={mockPortfolio} refreshPortfolio={refreshPortfolio} />
      </Box>
    </>
  );
}
