"use client";;
import { AdminGroupingParentQueryResponse } from "@rubiconcarbon/shared-types";
import Page from "@components/layout/containers/page";
import BooksComponent from "./books";

import type { JSX } from "react";

const Books = ({ booksByParentResponse }: { booksByParentResponse: AdminGroupingParentQueryResponse }): JSX.Element => {
  return (
    <Page>
      <BooksComponent booksByParentResponse={booksByParentResponse} />
    </Page>
  );
};

export default Books;
