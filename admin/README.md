# Rubicon Carbon Admin Platform

The Rubicon Carbon Admin Platform is an internal web application built with Next.js 15 and the App Router, designed for Rubicon Carbon's internal users to manage carbon credit operations, customer relationships, and business processes.

**🎯 Migration Status**: Successfully migrated from Next.js 13 Pages Router to Next.js 15 App Router with full feature parity and enhanced performance.

## 🏗️ Architecture & Technology Stack

### Core Technologies
- **Framework**: Next.js 15.3.4 with App Router
- **Runtime**: React 19.1.0
- **Language**: TypeScript 5.8.3
- **Styling**: Material-UI 5.11.8 + SCSS
- **State Management**: Jotai 2.8.4
- **Forms**: React Hook Form 7.56.4 + Class Validator + Yup + @hookform/resolvers
- **Data Fetching**: SWR 2.0.3 + Axios 1.3.2
- **Charts**: ECharts 5.4.3 + Plotly.js 2.12.0
- **Authentication**: Google OAuth + JWT + Secure Cookie Management
- **Monitoring**: OpenTelemetry + Custom Telemetry Provider

### Key Dependencies
- **UI Components**: Material-UI ecosystem (@mui/material, @mui/icons-material, @mui/lab, @mui/x-date-pickers)
- **Data Visualization**: ECharts, Material React Table, Plotly.js, @superset-ui/embedded-sdk
- **File Processing**: Filefy (CSV export), PapaParse (CSV import)
- **Date Handling**: date-fns, date-fns-tz, dayjs
- **Validation**: class-validator, class-transformer, yup
- **Animation**: Framer Motion 11.11.7
- **Utilities**: Lodash, fast-equals, react-use
- **Shared Libraries**: @rubiconcarbon/frontend-shared ^0.0.146, @rubiconcarbon/shared-types ^3.0.10

## 🚀 Getting Started

### Prerequisites
- Node.js 20+
- Yarn package manager
- Access to Rubicon Carbon's internal systems

### Development Setup

1. **Install dependencies**:
   ```bash
   yarn install
   ```

2. **Environment Configuration**:
   Create appropriate `.env` files based on deployment environment:
   - `.env.local` - Local development
   - `.env.deploy.dev` - Development environment
   - `.env.deploy.staging` - Staging environment
   - `.env.deploy.prod` - Production environment

3. **Start development server**:
   ```bash
   yarn dev
   ```

4. **Access the application**:
   Open [http://localhost:3000](http://localhost:3000) in your browser

### Build Commands

```bash
# Development build
yarn build

# Environment-specific builds
yarn build:local      # Local development build
yarn build:dev        # Development environment build
yarn build:testing    # Testing environment build
yarn build:staging    # Staging environment build
yarn build:sandbox    # Sandbox environment build
yarn build:prod       # Production environment build

# Bundle analysis
yarn build:analyze    # Build with bundle analyzer
```

## 📋 Available Scripts

### Development
- `yarn dev` - Start development server
- `yarn type-check` - Run TypeScript type checking
- `yarn type-check:watch` - Run TypeScript checking in watch mode

### Quality Assurance
- `yarn lint` - Run ESLint
- `yarn lint:fix` - Run ESLint with auto-fix
- `yarn check` - Run both type-check and lint
- `yarn precommit` - Pre-commit quality checks

### Build & Deployment
- `yarn build` - Production build
- `yarn start` - Start production server
- `yarn clean` - Clean build artifacts (.next, out directories)

## 🏢 Application Features

### Core Modules

#### 1. **Internal Platform Dashboard**
- Central hub for internal operations
- Quick access to all platform features
- Real-time system status and metrics

#### 2. **My Positions**
- Personal portfolio overview
- Holdings and position tracking
- Performance analytics

#### 3. **Trading Operations**
- **Market Data**: Inventory and market analysis
- **Quotes**: Price quote management
- **Transactions**: Buy/sell transaction processing
- **Bid/Ask Management**: Market making operations

#### 4. **Inventory Management**
- **Books**: Portfolio book management
- **RCT Portfolios**: Rubicon Carbon Token portfolios
- **Reserves**: Inventory reserve tracking
- **Portfolio Sandbox**: Model portfolio testing
- **Reconciliation**: Inventory reconciliation tools

#### 5. **Project Management**
- **Projects**: Carbon credit project administration
- **Project Vintages**: Vintage-specific project data
- **Image Management**: Project documentation and media

#### 6. **Retirements & Transfers**
- **Retirement Processing**: Carbon credit retirement workflows
- **Transfer Management**: Asset transfer operations
- **Composition Management**: Retirement portfolio composition
- **Calculator Tools**: Buffer and retirement calculators

#### 7. **Customer Management**
- **Customer Portal Users**: User account administration
- **Organizations**: Customer organization management
- **User Activity**: Activity monitoring and audit trails

#### 8. **Market Intelligence**
- **Market News**: Industry news and updates
- **Analytics**: Market trend analysis

#### 9. **Data Management**
- **Data Synchronization**: External data source integration
- **Google Sheets Integration**: Project data synchronization
- **Reporting Tools**: Data export and reporting

#### 10. **Notifications & Alerts**
- **System Notifications**: Platform-wide messaging
- **Alert Management**: Custom alert configuration

### Advanced Features

#### Authentication & Authorization
- Google OAuth integration with @react-oauth/google
- Role-based access control (RBAC) with PermissionEnum
- Permission-based feature access via AuthorizeServer component
- Secure cookie-based session management with compression (gzip)
- Automatic session refresh and inactivity timeout
- Server-side authentication token validation

#### Data Visualization
- Interactive charts and graphs (ECharts 5.4.3)
- Advanced plotting with Plotly.js 2.12.0
- Superset dashboard integration (@superset-ui/embedded-sdk)
- Material React Table for data grids
- Real-time data updates via SWR
- Export capabilities (CSV via Filefy, PDF)
- Customizable dashboards with responsive design

#### File Management
- Document upload and storage (AWS S3 integration)
- File type validation and security checks
- Secure file access with signed URLs
- Bulk operations and batch processing
- Image optimization with Next.js Image component

#### Performance Optimizations
- Server-side rendering (SSR) with App Router
- Static generation where applicable
- Automatic code splitting and lazy loading
- Image optimization (WebP, AVIF formats)
- Bundle analysis tools with @next/bundle-analyzer
- Memory optimizations and webpack chunking
- Package tree-shaking for major dependencies

## 🔧 Configuration

### Environment Variables

#### Required Variables
- `RUBICON_ADMIN_API_BASE_URL` - API backend URL
- `RUBICON_ADMIN_AUTH_URL` - Authentication service URL
- `RUBICON_ADMIN_STORAGE_BASE_URL` - File storage URL
- `RUBICON_ADMIN_GOOGLE_CLIENT_ID` - Google OAuth client ID
- `RUBICON_ADMIN_APP_NAME` - Application name
- `RUBICON_ADMIN_DEPLOYMENT_ENVIRONMENT` - Deployment environment

#### Optional Variables
- `RUBICON_ADMIN_BASE_URL` - Application base path
- `RUBICON_ADMIN_INACTIVITY_TIMEOUT` - Session timeout (default: 30 minutes)
- `RUBICON_ALL_PORTFOLIO_DEFAULT` - Default portfolio ID
- `RUBICON_FE_LIMIT_DEFAULT` - Default pagination limit

### TypeScript Configuration

The project uses strict TypeScript configuration with:
- **Target**: ES2022 with modern JavaScript features
- **Module Resolution**: Bundler mode for Next.js 15 compatibility
- **Experimental Decorators**: Enabled for class-validator integration
- **Path Aliases**: Comprehensive alias system for clean imports:
  - `@/*` → `./src/*`
  - `@app/*` → `./src/app/*`
  - `@components/*` → `./src/components/*`
  - `@hooks/*` → `./src/hooks/*`
  - `@providers/*` → `./src/providers/*`
  - `@utils/*` → `./src/utils/*`
  - `@models/*` → `./src/models/*`
  - `@constants/*` → `./src/constants/*`
  - `@uitypes/*` → `./src/types/*`
  - `@mappers/*` → `./src/mappers/*`
  - `@assets/*` → `./public/*`
- **Performance Optimizations**:
  - Incremental compilation enabled
  - Skip lib check for faster builds
  - `strictNullChecks: false` (temporarily for performance)
- **Memory Optimization**: Limited node_modules depth checking

### ESLint Configuration

Post-migration configuration with modern standards:
- **Next.js Rules**: Core web vitals + TypeScript-specific rules
- **Parser**: @typescript-eslint/parser with project-aware linting
- **Prettier Integration**: Consistent code formatting
- **Custom Rules**: Relaxed `@typescript-eslint/no-explicit-any` for flexibility

## 🏗️ Project Structure

```
admin/
├── src/
│   ├── app/                           # Next.js App Router (migrated from Pages Router)
│   │   ├── [level-one-nav-page]/     # Dynamic routing for main navigation
│   │   │   ├── [level-two-nav-page]/ # Nested dynamic routing
│   │   │   ├── books/               # Books management pages
│   │   │   ├── market-data/         # Market data pages
│   │   │   ├── organizations/       # Organization management
│   │   │   ├── portfolios/          # Portfolio management
│   │   │   ├── quotes/              # Quote management
│   │   │   ├── transactions/        # Transaction processing
│   │   │   └── users/               # User management
│   │   ├── api/                     # API routes
│   │   │   └── auth/                # Authentication API endpoints
│   │   ├── auth/                    # Authentication pages
│   │   │   └── cookie/              # Cookie management routes
│   │   ├── data-management/         # Data sync and reporting
│   │   ├── market-intelligence/     # Market news and analytics
│   │   ├── my-positions/           # Personal portfolio overview
│   │   ├── notifications-and-alerts/ # System notifications
│   │   ├── projects/               # Project management
│   │   ├── retirements/            # Retirement processing
│   │   ├── sandbox/                # Testing environment
│   │   ├── authorize-server.tsx    # Server component authorization
│   │   ├── authorize.tsx           # Client component authorization
│   │   ├── data-server.tsx         # Server-side data fetching
│   │   ├── home-client.tsx         # Home page client component
│   │   ├── layout.tsx              # Root layout
│   │   ├── main-layout.tsx         # Main application layout
│   │   ├── page.tsx                # Home page
│   │   └── providers.tsx           # Client-side providers
│   ├── components/                 # Reusable UI components
│   │   ├── ui/                     # Base UI components
│   │   ├── layout/                 # Layout components
│   │   ├── dashboard/              # Dashboard widgets
│   │   ├── charts/                 # Chart components
│   │   ├── forms/                  # Form components
│   │   └── [feature-specific]/     # Feature-organized components
│   ├── hooks/                      # Custom React hooks
│   │   ├── router-hooks.tsx        # App Router navigation hooks
│   │   └── [other-hooks].ts        # Various utility hooks
│   ├── providers/                  # Context providers
│   │   ├── auth-provider.tsx       # Authentication context
│   │   ├── navigation-menu/        # Navigation menu provider
│   │   ├── telemetry.tsx          # OpenTelemetry provider
│   │   └── [other-providers].tsx   # Various context providers
│   ├── utils/                      # Utility functions
│   │   ├── formatters/             # Data formatting utilities
│   │   ├── helpers/                # General helper functions
│   │   └── comparators/            # Comparison utilities
│   ├── types/                      # TypeScript type definitions
│   ├── constants/                  # Application constants
│   ├── models/                     # Data models and interfaces
│   ├── mappers/                    # Data transformation utilities
│   └── styles/                     # Global styles (SCSS)
├── public/                         # Static assets
│   ├── images/                     # Image assets
│   └── assets/                     # Other static files
├── .docker/                        # Docker configurations
│   ├── Dockerfile.local.build      # Local build Dockerfile
│   ├── Dockerfile.dev              # Development Dockerfile
│   ├── Dockerfile.staging          # Staging Dockerfile
│   └── Dockerfile.prod             # Production Dockerfile
├── .env.*                          # Environment configurations
├── next.config.js                  # Next.js configuration
├── tsconfig.json                   # TypeScript configuration
├── .eslintrc.js                    # ESLint configuration
└── package.json                    # Dependencies and scripts
```

## 🔒 Security Features

- **Authentication**: Google OAuth integration
- **Authorization**: Role-based access control
- **Session Management**: Secure session handling with timeout
- **CSRF Protection**: Built-in Next.js CSRF protection
- **Content Security**: Secure headers and content policies
- **File Upload Security**: Type validation and secure storage

## 📊 Performance Features

- **Bundle Optimization**: Tree shaking and code splitting
- **Image Optimization**: Next.js Image component with WebP/AVIF
- **Memory Management**: Webpack memory optimizations
- **Caching**: Intelligent caching strategies
- **Lazy Loading**: Component and route-based lazy loading

## 🧪 Development Guidelines

### Code Quality
- **TypeScript**: Strict mode enabled with ES2022 target
- **ESLint**: Next.js core web vitals + TypeScript rules
- **Prettier**: Consistent code formatting integration
- **Pre-commit Hooks**: Automated quality checks via `yarn precommit`
- **Path Aliases**: Clean import structure with comprehensive aliases

### Testing Strategy
- **Component Testing**: React Testing Library (planned)
- **Integration Testing**: Critical workflow testing (planned)
- **E2E Testing**: User journey testing (planned)
- **Type Safety**: Comprehensive TypeScript coverage

### Performance Monitoring
- **Bundle Analysis**: @next/bundle-analyzer integration
- **Telemetry**: OpenTelemetry with custom provider
- **Performance Metrics**: Real-time tracking and monitoring
- **Memory Optimization**: Webpack memory optimizations enabled
- **Build Performance**: Incremental TypeScript compilation

## 🚀 Deployment

The application supports multiple deployment environments with dedicated Docker configurations:

- **Local**: Development environment (`yarn build:local`, Dockerfile.local.build)
- **Development**: Internal development server (`yarn build:dev`, Dockerfile.dev)
- **Testing**: QA testing environment (`yarn build:testing`, Dockerfile.testing)
- **Staging**: Pre-production environment (`yarn build:staging`, Dockerfile.staging)
- **Sandbox**: Sandbox environment for testing (`yarn build:sandbox`, Dockerfile.sandbox)
- **Production**: Live production environment (`yarn build:prod`, Dockerfile.prod)

### Docker Configuration
- **Multi-stage builds**: Optimized build and runtime stages
- **Node.js 20**: Latest LTS runtime
- **Standalone output**: Next.js standalone mode for containerization
- **Security**: Non-root user execution (nextjs:nodejs)
- **Environment Variables**: Git commit tracking and environment-specific configs
- **Port**: Standardized on port 4000 across all environments

Each environment has its own configuration, build process, and Docker image with proper labeling for tracking.

## 🎯 Current State & Health

### ✅ System Status
- **Build Status**: ✅ All builds passing
- **TypeScript**: ✅ No type errors (verified with `yarn type-check`)
- **Dependencies**: ✅ All dependencies up-to-date and compatible
- **Migration**: ✅ Next.js 15 App Router migration complete
- **Performance**: ✅ Optimized for production with modern tooling

### 🚀 Recent Improvements (2024)
- **Framework Upgrade**: Successfully migrated to Next.js 15 with App Router
- **React 19**: Upgraded to latest React with improved performance
- **Authentication**: Enhanced security with server-side cookie management
- **Telemetry**: Integrated OpenTelemetry for comprehensive monitoring
- **Build System**: Optimized Docker configurations for all environments
- **Developer Experience**: Improved TypeScript configuration and tooling

### 📊 Performance Metrics
- **Bundle Size**: Optimized with tree-shaking and code splitting
- **Build Time**: ~2.5 seconds for type checking
- **Memory Usage**: Optimized with webpack memory optimizations
- **Load Time**: Enhanced with App Router SSR capabilities

## 📚 Additional Resources

- [Next.js 15 Documentation](https://nextjs.org/docs)
- [React 19 Documentation](https://react.dev/)
- [Material-UI Documentation](https://mui.com/)
- [TypeScript Documentation](https://www.typescriptlang.org/docs/)
- [React Hook Form Documentation](https://react-hook-form.com/)
- [OpenTelemetry Documentation](https://opentelemetry.io/docs/)

## 🔄 Migration History

### Next.js 13 → Next.js 15 Migration (Completed)
- ✅ **Pages Router → App Router**: Complete migration to App Router architecture
- ✅ **React 18 → React 19**: Upgraded to latest React version
- ✅ **TypeScript 5.8**: Updated to latest TypeScript with modern features
- ✅ **Dynamic Routing**: Implemented `[level-one-nav-page]` and `[level-two-nav-page]` structure
- ✅ **Server Components**: Proper separation of server and client components
- ✅ **Authentication**: Migrated to cookie-based authentication with server-side validation
- ✅ **Performance**: Enhanced with App Router optimizations and modern bundling
- ✅ **Dependencies**: Updated all major dependencies to latest compatible versions

### Key Architectural Improvements
- **Server-Side Authentication**: Secure cookie management with compression
- **Component Architecture**: Clear separation between server and client components
- **Routing**: Dynamic routing with nested navigation support
- **Performance**: Memory optimizations and bundle splitting
- **Developer Experience**: Enhanced TypeScript configuration and tooling

## 🤝 Contributing

This is an internal application for Rubicon Carbon. For development guidelines and contribution processes, please refer to the internal development documentation.

### Development Workflow
1. **Setup**: `yarn install` to install dependencies
2. **Development**: `yarn dev` to start development server
3. **Quality**: `yarn check` to run type-check and lint
4. **Build**: `yarn build` to create production build
5. **Deploy**: Environment-specific builds with Docker

---

**Note**: This application is part of the Rubicon Carbon platform ecosystem and requires access to internal systems and APIs for full functionality. The migration to Next.js 15 App Router has been successfully completed with enhanced performance and modern architecture.
