/* third party */
import { DecimalType, Entity, IntegerType, ManyToOne, OptionalProps, Property } from '@mikro-orm/core';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
import { IsBoolean, IsOptional, IsString } from 'class-validator';
import Decimal from 'decimal.js';
/* env */
import { environment } from '@env/environment';
/* app */
import { IsDateRange } from '@app/helpers';
import { TransformDateRangeISO, TransformDecimal } from '@app/helpers/transforms.helper';
import { IsDecimalGreaterThanOrEqualTo, IsIntGreaterThan } from '@app/validators';
import { BaseEntity } from './base.entity';
import { ModelPortfolio } from './model-portfolio.entity';
import { User } from './user.entity';
import { BufferCategory } from './buffer-category.entity';
import { Project } from './project.entity';
import { ProjectVintage } from './project-vintage.entity';

@Entity({ tableName: `model_portfolio_components`, schema: environment.db.schema.rubicon })
export class ModelPortfolioComponent extends BaseEntity {
  [OptionalProps]?: 'createdAt' | 'isDeleted' | 'updatedAt';

  ///////////////////////////////////////// Fields //////////////////////////////////////////

  @Expose()
  @IsIntGreaterThan(0)
  @ApiProperty({ type: 'integer', example: 1500 })
  @Property({ type: IntegerType, fieldName: 'quantity' })
  amountAllocated!: number;

  @Expose()
  @TransformDecimal()
  @IsDecimalGreaterThanOrEqualTo(new Decimal(0))
  @IsOptional()
  @ApiPropertyOptional({
    type: String,
    format: 'decimal',
    nullable: true,
    minimum: 0,
    exclusiveMinimum: false,
  })
  @Property({ type: DecimalType, nullable: true })
  bufferPercentage?: Decimal;

  @Expose()
  @TransformDecimal()
  @IsDecimalGreaterThanOrEqualTo(new Decimal(0))
  @IsOptional()
  @ApiPropertyOptional({ type: 'decimal', minimum: 0, nullable: true, exclusiveMinimum: false, example: 15.22 })
  @Property({ type: DecimalType, nullable: true })
  costBasis?: Decimal;

  @Expose()
  @TransformDecimal()
  @IsDecimalGreaterThanOrEqualTo(new Decimal(0))
  @IsOptional()
  @ApiPropertyOptional({ type: 'decimal', minimum: 0, nullable: true, exclusiveMinimum: false, example: 15.22 })
  @Property({ type: DecimalType, nullable: true })
  overrideMTM?: Decimal;

  @Expose()
  @IsBoolean()
  @ApiProperty({ type: Boolean })
  @Property({ index: true })
  isBufferComponent!: boolean;

  @Expose()
  @IsBoolean()
  @IsOptional()
  @Property({ index: true })
  isDeleted = false;

  @Expose()
  @TransformDecimal()
  @IsDecimalGreaterThanOrEqualTo(new Decimal(0))
  @IsOptional()
  @ApiPropertyOptional({
    type: String,
    format: 'decimal',
    nullable: true,
    minimum: 0,
    exclusiveMinimum: false,
    example: 15000,
  })
  @Property({ type: DecimalType, nullable: true })
  portfolioManagerEstimate?: Decimal; // aka mtm

  @Expose()
  @IsString()
  @IsOptional()
  @ApiPropertyOptional({ type: String })
  @Property({ nullable: true })
  projectName?: string;

  @Expose()
  @IsString()
  @IsOptional()
  @ApiPropertyOptional({ type: String })
  @Property({ nullable: true })
  registryProjectId?: string;

  @Expose()
  @TransformDateRangeISO()
  @IsDateRange()
  @IsOptional()
  @ApiProperty({ type: String, format: 'start.toISOString() - end.toISOString()' })
  @Property({ type: String, fieldName: 'project_vintage_interval', nullable: true })
  vintageInterval?: string;

  ////////////////////////////////////// Relationships //////////////////////////////////////

  @ManyToOne(() => BufferCategory, { index: true, nullable: true })
  bufferCategory?: BufferCategory;

  @ManyToOne(() => User, { index: true })
  createdBy!: User;

  @ManyToOne(() => ModelPortfolio, { index: true })
  modelPortfolio!: ModelPortfolio;

  @ManyToOne(() => Project, { index: true, nullable: true })
  project?: Project;

  @ManyToOne(() => ProjectVintage, { index: true, fieldName: 'project_vintage_id', nullable: true })
  vintage?: ProjectVintage;

  @ManyToOne(() => User, { index: true })
  updatedBy!: User;
}
