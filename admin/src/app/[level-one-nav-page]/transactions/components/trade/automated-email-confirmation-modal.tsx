import GenericDialog from "@components/ui/generic-dialog/generic-dialog";
import MatIcon from "@components/ui/mat-icon/mat-icon";
import { classcat } from "@rubiconcarbon/frontend-shared";
import { Box, Typography } from "@mui/material";
import AutomatedEmailConfirmation from "./automated-email-confirmation";
import { AdminTradeConfirmationResponse } from "@rubiconcarbon/shared-types";

import type { JSX } from "react";

import classes from "../../styles/automated-email-confirmation-modal.module.scss";
import dialogClasses from "../../styles/dialog.module.scss";

type AutomatedEmailConfirmationModalProps = {
  open: boolean;
  confirmation?: AdminTradeConfirmationResponse;
  onClose: () => void;
};

const AutomatedEmailConfirmationModal = ({
  open,
  confirmation,
  onClose,
}: AutomatedEmailConfirmationModalProps): JSX.Element => (
  <GenericDialog
    open={open}
    title={
      <Typography variant="h6" fontWeight={500}>
        Automated Email Confirmation Details for <strong>{confirmation?.uiKey}</strong>
      </Typography>
    }
    dismissIcon={<MatIcon value="close" variant="round" size={24} color="action" />}
    positiveAction={{
      buttonText: "Close",
      onClick: onClose,
    }}
    classes={{
      root: classcat([dialogClasses.Dialog, { [dialogClasses.Binding]: true }]),
      title: dialogClasses.StatusTitle,
      actions: classcat([dialogClasses.StatusActions, classes.ActionsOverride]),
    }}
    onClose={onClose}
  >
    <Box padding={2}>
      <AutomatedEmailConfirmation loading={false} confirmation={confirmation} ignoreReceipients />
    </Box>
  </GenericDialog>
);

export default AutomatedEmailConfirmationModal;
