import { AuthorizeServer } from "@app/authorize-server";
import {
  OrderByDirection,
  PermissionEnum,
  PurchaseOrderByOptions,
  AdminPurchaseQueryResponse,
  PurchaseRelations,
  AdminTradeQueryResponse,
  TradeRelation,
  TransactionOrderByOptions,
  AdminTransactionQueryResponse,
  TransactionType,
} from "@rubiconcarbon/shared-types";
import Transactions from "./components";
import { withPermissionHandling } from "@app/data-server";
import { baseApiRequest, generateQueryParams, getUserPermissions } from "@app/libs/server";
import { SERVER_PAGINATION_LIMIT } from "@constants/constants";
import { isValidElement, type JSX } from "react";

/**
 * Transactions Page
 *
 * This is a server component that renders the Transactions page
 */
export default async function TransactionsPage(): Promise<JSX.Element> {
  const userPermissions = await getUserPermissions();

  const transactionsResponse = await withPermissionHandling(
    async () =>
      baseApiRequest<AdminTransactionQueryResponse>(
        `admin/transactions?${generateQueryParams({
          limit: SERVER_PAGINATION_LIMIT,
          includeTotalCount: true,
          types: [
            userPermissions?.includes(PermissionEnum.CUSTOMER_SALES_READ) ? TransactionType.PURCHASE : null,
            userPermissions?.includes(PermissionEnum.TRADES_READ) ? TransactionType.TRADE : null,
          ]?.filter((entry) => !!entry),
          orderBys: [`${TransactionOrderByOptions.CREATED_AT}:${OrderByDirection.DESC_NULLS_LAST}`],
        })}`,
      ),
    [PermissionEnum.CUSTOMER_SALES_READ, PermissionEnum.TRADES_READ],
    { check: "some" },
  );

  const purchasesResponse = await withPermissionHandling(
    async () =>
      baseApiRequest<AdminPurchaseQueryResponse>(
        `admin/purchases?${generateQueryParams({
          limit: SERVER_PAGINATION_LIMIT,
          includeTotalCount: true,
          orderBys: [`${PurchaseOrderByOptions.CREATED_AT}:${OrderByDirection.DESC_NULLS_LAST}`],
          includeRelations: [PurchaseRelations.ASSETS, PurchaseRelations.CUSTOMER_PORTFOLIO],
        })}`,
      ),
    [PermissionEnum.CUSTOMER_SALES_READ],
    { ignoreError: true },
  );

  const tradesResponse = await withPermissionHandling(
    async () =>
      baseApiRequest<AdminTradeQueryResponse>(
        `admin/trades?${generateQueryParams({
          limit: SERVER_PAGINATION_LIMIT,
          includeTotalCount: true,
          includeRelations: [TradeRelation.BOOK, TradeRelation.PROJECT_VINTAGE],
        })}`,
      ),
    [PermissionEnum.TRADES_READ],
    { ignoreError: true },
  );

  // Check if the result is a server error
  if (isValidElement(transactionsResponse)) return transactionsResponse;
  if (isValidElement(purchasesResponse)) return purchasesResponse;
  if (isValidElement(tradesResponse)) return tradesResponse;

  return (
    <AuthorizeServer partiallyAuthorize permissions={[PermissionEnum.CUSTOMER_SALES_READ, PermissionEnum.TRADES_READ]}>
      <Transactions
        transactionsResponse={transactionsResponse as AdminTransactionQueryResponse}
        purchasesResponse={purchasesResponse as AdminPurchaseQueryResponse}
        tradesResponse={tradesResponse as AdminTradeQueryResponse}
      />
    </AuthorizeServer>
  );
}
