import { AdminAllocationResponse, BookType, uuid, AdminGroupingParentResponse } from "@rubiconcarbon/shared-types";

export type ExtendedAllocation = { bookId: uuid; bookName: string; bookType: BookType } & AdminAllocationResponse;

export type BookParentGrouping = {
  compositeId: string; // will mostly be one id but RCT will have it concatenated with "," or "%2C"
  total: number; // total value | settled - unsettled
} & AdminGroupingParentResponse;
