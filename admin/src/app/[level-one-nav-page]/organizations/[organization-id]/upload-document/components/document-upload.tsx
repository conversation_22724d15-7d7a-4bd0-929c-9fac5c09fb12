import { Box, Grid, Typography } from "@mui/material";
import {
  AdminOrganizationResponse,
  DocumentType,
  AdminDocumentUpdateRequest,
  AdminDocumentUploadUrlRequest,
  uuid,
} from "@rubiconcarbon/shared-types";
import { useRouter } from "next/router";
import UploaderWidget from "@components/ui/uploader/components/UploaderWidget";
import { useCallback, useMemo, useState, type JSX } from "react";
import useSnackbarVariants from "@hooks/use-enqueue-variant";
import { OnFileUploadSuccessMetaData } from "@components/ui/uploader/types/hook";
import useDocumentsApi from "@hooks/use-documents-api";
import usePerformantEffect from "@/hooks/use-performant-effect";
import { useGetSetState } from "react-use";
import { useLogger } from "@providers/logging";

import classes from "../styles/document-upload.module.scss";

export default function DocumentUpload({
  organizationResponse,
}: {
  organizationResponse: AdminOrganizationResponse;
}): JSX.Element {
  const { logger } = useLogger();
  const router = useRouter();
  const { enqueueSuccess, enqueueError } = useSnackbarVariants();

  const [getUploadLinkPayload, setUploadLinkPayload] = useGetSetState<AdminDocumentUploadUrlRequest>();
  const [updatePayload, setUpdatePayload] = useState<AdminDocumentUpdateRequest>();

  const name = useMemo(() => organizationResponse.name, [organizationResponse]);
  const id = useMemo(() => organizationResponse.id, [organizationResponse]);

  const { fetching, fetch, retrieveUploadLink, update } = useDocumentsApi({
    query: {
      organizationId: id as uuid,
      types: [DocumentType.MASTER_AGREEMENT],
    },
    updatePayload,
    uploadLinkPayload: getUploadLinkPayload(),
    onUploadLinkRetrievalError: (error: any) => {
      enqueueError("Unable to get upload link");
      logger.error(`Unable to get upload link: ${error.message}`, {});
    },
    onUpdateSuccess: async () => {
      enqueueSuccess("Successfully uploaded file");
      await fetch();
    },
    onUpdateError: (error: any) => {
      enqueueError("Successful upload but was unable to update file details");
      logger.error(`Successful upload but was unable to update file details: ${error?.message}`, {});
    },
  });

  usePerformantEffect(() => {
    if (!fetching && !!id) {
      setTimeout(async () => await fetch());
    }
  }, [id]);

  const getS3UploadApiLink = async (file: File): Promise<string> => {
    setUploadLinkPayload({
      organizationId: id as uuid,
      filename: file.name,
      type: DocumentType.MASTER_AGREEMENT,
      isPublic: true,
    });

    const { uploadUrl } = await retrieveUploadLink();

    return uploadUrl;
  };

  const onFileUploadSuccess = useCallback(
    async (file: File, metadata: OnFileUploadSuccessMetaData): Promise<void> => {
      setUpdatePayload({
        id: uuid(metadata?.s3FileId),
        organizationId: id as uuid,
        filename: file.name,
        type: DocumentType.MASTER_AGREEMENT,
        isPublic: true,
      });

      setTimeout(async () => await update());
    },
    [id, update, setUpdatePayload],
  );

  const onFileUploadError = useCallback((): void => {
    enqueueError("Unable to upload file");
  }, [enqueueError]);

  return (
    <Box className={classes.Container}>
      <Grid className={classes.Content} container direction="column" gap={3}>
        <Grid item>
          <Typography variant="body2">File details</Typography>
        </Grid>
        <Grid item container direction="row">
          <Grid item container md={3} xs={6} direction="row" gap={1}>
            <Typography className={classes.Label} variant="body2">
              Organization
            </Typography>
            <Typography variant="body2">{name}</Typography>
          </Grid>
        </Grid>
        <UploaderWidget
          inputId="organization-uploads"
          maxFiles={10}
          canDragAndDrop
          cancelButtonText="back"
          uploadLink={getS3UploadApiLink}
          allowedExtensions={["application/pdf"]}
          clearOnFileUploadSuccess
          uploadConfirmation={{
            title: "Confirm document upload",
            content: (
              <span>
                Are you sure you want to share file with organization: <b>{name}</b>
              </span>
            ),
          }}
          onFileUploadSuccess={onFileUploadSuccess}
          onFileUploadError={onFileUploadError}
          handleUploadClosure={() => router.back()}
        />
      </Grid>
    </Box>
  );
}
