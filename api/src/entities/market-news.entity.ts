/* database */
import { Collection, Entity, ManyToMany, ManyToOne, Property } from '@mikro-orm/core';
/* config */
import { environment } from '@env/environment';
import { Expose } from 'class-transformer';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsNonEmptyString } from '@app/helpers/validations.helper';
import { Project } from './project.entity';
import { TransformDateISO } from '@app/helpers/transforms.helper';
import { BaseEntity } from './base.entity';
import { User } from './user.entity';
import { IsArray, IsBoolean, IsDate, IsEnum, IsNumber, IsOptional, IsString, IsUUID } from 'class-validator';
import { MarketNewsScope, uuid } from '@rubiconcarbon/shared-types';

@Entity({ tableName: `market_news`, schema: environment.db.schema.rubicon })
export class MarketNews extends BaseEntity {
  ///////////////////////////////////////// Fields //////////////////////////////////////////
  @Expose()
  @IsDate()
  @TransformDateISO()
  @ApiProperty({ type: Date, format: 'iso', readOnly: true, example: '2025-07-28T18:09:45.541Z' })
  @Property({ index: true })
  articleDate!: Date;

  @Expose()
  @IsNonEmptyString()
  @ApiProperty({
    type: String,
    description: 'News Header',
    example: 'Aperture/Black Mesa Joint Project to Tackle Direct Air Capture',
  })
  @Property()
  header!: string;

  @Expose()
  @IsString()
  @ApiProperty({ type: String, description: 'Source of article', example: 'Example Site' })
  @Property({ type: 'string' })
  source!: string;

  @Expose()
  @IsString()
  @IsOptional()
  @ApiPropertyOptional({
    type: String,
    description: 'Hitword if sourced from AI engine',
    example: 'climate',
    nullable: true,
  })
  @Property({ nullable: true })
  hitword?: string;

  @Expose()
  @IsBoolean()
  @IsOptional()
  @ApiPropertyOptional({ type: Boolean, description: 'Manual designation' })
  @Property()
  isIrrelevant = false;

  @Expose()
  @IsUUID()
  @IsOptional()
  @ApiPropertyOptional({
    type: String,
    format: 'uuid',
    nullable: true,
    description: 'uuid of associated organization (if any)',
    example: uuid(),
  })
  @Property({ type: 'string', nullable: true })
  organizationId?: uuid;

  @Expose()
  @IsNumber()
  @IsOptional()
  @ApiPropertyOptional({ type: Number, nullable: true })
  @Property({ nullable: true })
  score?: number;

  @Expose()
  @IsNonEmptyString()
  @ApiProperty({ type: String, description: 'Brief article summary' })
  @Property()
  summary!: string;

  @Expose()
  @IsNonEmptyString()
  @ApiProperty({ type: String, description: 'News Source URL', example: 'example.com/climate-news' })
  @Property({ unique: true })
  url!: string;

  @Expose()
  @IsEnum(MarketNewsScope, { each: true })
  @IsArray()
  @ApiProperty({
    type: [String],
    enum: MarketNewsScope,
    description: 'Scope of relevance',
    example: [MarketNewsScope.CUSTOMER_PORTAL, MarketNewsScope.CORPORATE_WATCHLIST],
  })
  @Property({ type: 'array' })
  scopes!: MarketNewsScope[];

  @Property({ index: true })
  isDeleted = false;

  ////////////////////////////////////// Relationships //////////////////////////////////////

  /**
   * {@link Project}.
   * @category Relationship
   */
  @ManyToMany({ entity: () => Project, pivotTable: 'market_news_projects' })
  projects = new Collection<Project>(this);

  /**
   * {@link User}
   * @category Relationship
   **/
  @ManyToOne(() => User, { index: true })
  createdBy!: User;

  /**
   * {@link User}
   * @category Relationship
   **/
  @ManyToOne(() => User, { index: true })
  updatedBy!: User;
}
