import { Migration } from '@mikro-orm/migrations';
import { environment } from '@env/environment';

const numeric = `numeric(${environment.app.decimal.precision}, ${environment.app.decimal.scale})`;

export class Migration20250813091400 extends Migration {
  async up(): Promise<void> {
    this.addSql(
      `ALTER TABLE "${environment.db.schema.rubicon}".model_portfolio_components add column override_mtm ${numeric};`,
    );
  }
}
