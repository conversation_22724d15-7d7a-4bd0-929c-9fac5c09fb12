import { useAtomValue, useSet<PERSON>tom } from "jotai";
import { selectedRowsAtom, updateSelectedRowsAtom } from "../actions";
import { externalAtom } from "../structure";
import { Checkbox } from "@mui/material";
import { GenericTableRowModel } from "../../types/generic-table-row-model";
import { MouseEvent, type JSX } from "react";

type SelectIconButtonProps<M> = {
  row: GenericTableRowModel<M>;
};

const SelectIconButton = ({ row }: SelectIconButtonProps<any>): JSX.Element => {
  const record = useAtomValue(selectedRowsAtom);
  const { selectOn } = useAtomValue(externalAtom);

  const set = useSetAtom(updateSelectedRowsAtom);

  const selected = record?.[row?.id];
  const shouldSelect = selected || (row?.creating && selectOn?.create) || (row?.editing && selectOn?.edit);

  return (
    <Checkbox
      checked={!!shouldSelect}
      onClick={(event: MouseEvent<HTMLButtonElement>) => {
        event?.preventDefault();
        event?.stopPropagation();

        set({ id: row?.id });
      }}
    />
  );
};

export default SelectIconButton;
