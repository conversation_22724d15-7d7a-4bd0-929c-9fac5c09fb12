import { AuthorizeServer } from "@app/authorize-server";
import { AdminAlertQueryResponse, AdminNotificationSubscriptionQueryResponse, PermissionEnum } from "@rubiconcarbon/shared-types";
import { withErrorHandling } from "@app/data-server";
import NotificationsAndAlerts from "./components";
import { ALERT_SUBSCRIPTIONS_API_URL, NOTIFICATION_SUBSCRIPTIONS_API_URL } from "./constants/subscription";
import { isValidElement, type JSX } from "react";
import { baseApiRequest } from "../libs/server";

/**
 * Notification And Alerts Page
 *
 * This is a server component that fetches initial notification subscription data
 * and passes it to the client component
 */
export default async function NotificationsAndAlertsPage(): Promise<JSX.Element> {
  const notificationSubsResponse = await withErrorHandling(async () =>
    baseApiRequest<AdminNotificationSubscriptionQueryResponse>(NOTIFICATION_SUBSCRIPTIONS_API_URL),
  );

  const alertSubsResponse = await withErrorHandling(async () =>
    baseApiRequest<AdminAlertQueryResponse>(ALERT_SUBSCRIPTIONS_API_URL),
  );

  // Check if the result is a server error
  if (isValidElement(notificationSubsResponse)) return notificationSubsResponse;
  if (isValidElement(alertSubsResponse)) return alertSubsResponse;

  return (
    <AuthorizeServer permissions={[PermissionEnum.LOGIN]}>
      <NotificationsAndAlerts notificationSubsResponse={notificationSubsResponse as AdminNotificationSubscriptionQueryResponse} alertSubsResponse={alertSubsResponse as AdminAlertQueryResponse} />
    </AuthorizeServer>
  );
}
