"use client";

import { AdminOrganizationQueryResponse, AdminGroupingParentQueryResponse, AdminTradeResponse } from "@rubiconcarbon/shared-types";
import useBreadcrumbs from "@providers/breadcrumb-provider";
import { useEffect, type JSX } from "react";
import TradeForm from "../../../components/trade/form";
import { Nullable } from "@rubiconcarbon/frontend-shared";

export default function EditTransaction({
  tradeResponse,
  organizationsResponse,
  booksByParentResponse,
  type,
}: {
  tradeResponse: AdminTradeResponse;
  organizationsResponse: AdminOrganizationQueryResponse;
  booksByParentResponse: AdminGroupingParentQueryResponse;
  type: string;
}): Nullable<JSX.Element> {
  const { updateBreadcrumbName } = useBreadcrumbs();

  useEffect(() => {
    updateBreadcrumbName?.("Edit", type === "trade" ? "Edit Trade" : "");
  }, [type, updateBreadcrumbName]);

  return type === "trade" ? (
    <TradeForm
      tradeResponse={tradeResponse}
      organizationsResponse={organizationsResponse}
      booksByParentResponse={booksByParentResponse}
    />
  ) : null;
}
