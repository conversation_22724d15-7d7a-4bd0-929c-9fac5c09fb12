import { Drawer } from "@mui/material";
import NavMenuList from "./nav-menu-list";
import { MenuProps } from "../types/menu-props";

import type { JSX } from "react";

import classes from "../styles/main-header.module.scss";

const FullscreenMenu = ({ open, onClose }: MenuProps): JSX.Element => {
  return (
    <Drawer
      anchor="left"
      open={open}
      hideBackdrop={true}
      onClose={onClose}
      classes={{
        root: classes.FullscreenMenuRoot,
        paper: classes.FullscreenMenuPaper,
      }}
    >
      <NavMenuList type="mobile" onMenuItemClick={onClose} />
    </Drawer>
  );
};

export default FullscreenMenu;
