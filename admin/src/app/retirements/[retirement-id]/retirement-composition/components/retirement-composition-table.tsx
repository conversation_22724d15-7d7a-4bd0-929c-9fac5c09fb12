import React, { useState, useEffect, useCallback, useMemo, SyntheticEvent, type JSX } from "react";
import useS<PERSON> from "swr";
import { isEmpty, isNil } from "lodash";
import {
  PermissionEnum,
  uuid,
  AdminBookResponse,
  AdminRetirementResponse,
  AdminGroupedAllocationWithNestedResponse,
  AdminAllocationResponse,
  BookRelations,
  AdminAssetResponse,
} from "@rubiconcarbon/shared-types";
import {
  Box,
  TableContainer,
  Paper,
  Table,
  TableBody,
  TableRow,
  TableCell,
  IconButton,
  TableHead,
  Autocomplete,
  Typography,
  TextField,
  Stack,
} from "@mui/material";
import { MISSING_DATA, RCT_STANDARD, SUSPENDED } from "@constants/constants";
import ActionButton from "@components/ui/action-button/action-button-enhanced";
import SearchAppBar from "@components/ui/search/enhanced-search";
import UndoIcon from "@mui/icons-material/Undo";
import AddIcon from "@mui/icons-material/Add";
import integerFormat from "@/utils/formatters/integer-format";
import { NumericFormat } from "react-number-format";
import CreateIcon from "@mui/icons-material/Create";
import DeleteIcon from "@mui/icons-material/Delete";
import CheckIcon from "@mui/icons-material/Check";
import CancelIcon from "@mui/icons-material/Cancel";
import { TransactionProjectVintage } from "@models/transaction-project-vintage";
import { convertAmountToNumber } from "@utils/helpers/general/general";
import dateRangeFormatter from "@/utils/formatters/date-range-formatter";
import RetirementCompositionVintage from "@/models/portfolio-composition-vintage";
import useSnackbarVariants from "@hooks/use-enqueue-variant";
import CompositionVintage from "@models/composition-vintage";
import { Project, mapProject } from "@models/project";
import { alphabeticalThenNumberSort } from "@utils/comparators/comparator";
import RetirementCompositionModal from "./retirement-composition-dialog";
import { useRouter } from "next/navigation";
import { isNothing, Maybe, MaybeNothing, Nullable, toNumber, uniqueByKeys } from "@rubiconcarbon/frontend-shared";
import {
  IProject,
  IVintage,
  IQuantity,
  ProjectVintageTableRow,
  ExtendedTrimmedProjectVintageResponse,
} from "./retirement-composition-model";
import RetirementCompositionUpload from "./retirement-composition-upload";
import SuspendedChip from "@components/ui/suspended-chip/suspended-chip";
import RCTEligibilityChip from "@components/ui/rct-eligibility-chip/rct-eligibility-chip";
import TableBox from "@components/ui/table-box/table-box";
import COLORS from "@components/ui/theme/colors";

const newRowStyle = {
  verticalAlign: "top",
};

const itemStyle = {
  paddingTop: "10px",
};

const isRowContainsValue = (row: TransactionProjectVintage, searchString: string): boolean => {
  if (
    row.projectName?.toUpperCase().includes(searchString) ||
    row.vintageName?.toUpperCase().includes(searchString) ||
    row.registryProjectId?.toUpperCase().includes(searchString) ||
    row.amountTransacted?.toString().toUpperCase().includes(searchString) ||
    row.location?.toUpperCase().includes(searchString) ||
    row.projectType?.toUpperCase().includes(searchString) ||
    (row?.suspended === true && SUSPENDED.includes(searchString.toUpperCase())) ||
    (row?.rctStandard === true && RCT_STANDARD.includes(searchString.toUpperCase()))
  )
    return true;

  return false;
};

function buildUpdatedRowName(row: TransactionProjectVintage): string {
  return `${row?.projectName} - ${dateRangeFormatter(row?.interval)}`;
}

function calcOriginalAmountSum(rows: TransactionProjectVintage[]): number {
  return rows.reduce(function (a, b) {
    return a + b.amountTransacted;
  }, 0);
}

function Row(props: {
  row: TransactionProjectVintage;
  idx: uuid;
  editIdx?: uuid;
  deletedVintages?: ProjectVintageTableRow[];
  startEditing: (idx: uuid) => void;
  stopEditing: () => void;
  deleteHandler: (id: uuid) => void;
  currencyOnChangeHandler: (value: number, id: uuid) => void;
  validateQuantity: (quantity: number, amountUnallocated: number, deletedAmountTransacted: number) => IQuantity;
}): JSX.Element {
  const {
    row,
    idx,
    editIdx,
    deletedVintages,
    startEditing,
    stopEditing,
    deleteHandler,
    currencyOnChangeHandler,
    validateQuantity,
  } = props;

  const [quantity, setQuantity] = useState<IQuantity>({
    value: row.amountTransacted,
    error: false,
    message: "",
  });
  const [initialValue, setInitialValue] = useState<number>();

  useEffect(() => {
    const { amountTransacted = 0 } = deletedVintages?.find(({ id }) => id === row.id) || {};
    setQuantity({
      value: row.amountTransacted,
      error: false,
      message: `${integerFormat(amountTransacted + (row.amountUnallocated ?? 0))} available`,
    });
    setInitialValue(row.amountTransacted);
  }, [startEditing, row.amountTransacted, row.amountUnallocated, deletedVintages, row.id]);

  const currencyRowOnChangeHandler = (event: React.ChangeEvent<HTMLInputElement>): void => {
    const { amountTransacted = 0 } = deletedVintages?.find(({ id }) => id === row.id) || {};

    const value = toNumber(event.target.value);
    const newQuantity = validateQuantity(value, row.amountUnallocated ?? 0, amountTransacted);
    setQuantity(newQuantity);
  };

  const cancelEditHandler = (): void => {
    stopEditing();
    currencyOnChangeHandler(initialValue ?? 0, row.id);
  };

  const stopEditingHandler = (): void => {
    if (!quantity.error) currencyOnChangeHandler(quantity.value ?? 0, row.id);

    stopEditing();
  };

  const currentlyEditing = idx === editIdx;

  return (
    <React.Fragment>
      <TableRow sx={{ verticalAlign: "top" }}>
        <TableCell>
          <Typography variant="body2" component="div" sx={{ ...itemStyle, marginTop: "2px" }}>
            <RCTEligibilityChip vintage={row as any} />
          </Typography>
        </TableCell>
        <TableCell align="left">
          <Typography variant="body2" component="div" sx={itemStyle}>
            {row?.registryProjectId} - {row?.projectName}
            <Stack direction="row" gap={1}>
              <Maybe condition={row?.suspended === true}>
                <SuspendedChip />
              </Maybe>
            </Stack>
          </Typography>
        </TableCell>
        <TableCell align="left">
          <Typography variant="body2" component="div" sx={itemStyle}>
            {row?.projectType}
          </Typography>
        </TableCell>
        <TableCell align="left">
          <Typography variant="body2" component="div" sx={itemStyle}>
            {row?.location ?? MISSING_DATA}
          </Typography>
        </TableCell>
        <TableCell align="left">
          <Typography variant="body2" component="div" sx={itemStyle}>
            {row?.vintageName}
          </Typography>
        </TableCell>
        <TableCell align="left">
          {currentlyEditing ? (
            <NumericFormat
              size="small"
              defaultValue={row.amountTransacted}
              decimalScale={0}
              inputProps={{ maxLength: 20, style: { fontSize: 14 } }}
              allowNegative={false}
              customInput={TextField}
              type="text"
              thousandSeparator={","}
              value={quantity.value}
              onChange={(e) => currencyRowOnChangeHandler(e)}
              helperText={quantity.message}
              error={quantity.error}
            />
          ) : (
            <Typography variant="body2" component="div" sx={itemStyle}>
              {integerFormat(row.amountTransacted)}
            </Typography>
          )}
        </TableCell>

        <TableCell>
          {currentlyEditing ? (
            <Box sx={{ width: "70px" }}>
              <IconButton
                sx={{ color: COLORS.rubiconGreen }}
                disabled={quantity.error}
                edge="start"
                onClick={stopEditingHandler}
              >
                <CheckIcon />
              </IconButton>
              <IconButton sx={{ marginLeft: "1px", color: COLORS.red }} edge="start" onClick={cancelEditHandler}>
                <CancelIcon />
              </IconButton>
            </Box>
          ) : (
            <IconButton sx={{ color: COLORS.rubiconGreen }} edge="start" onClick={() => startEditing(idx)}>
              <CreateIcon />
            </IconButton>
          )}
        </TableCell>
        <TableCell>
          <IconButton sx={{ color: COLORS.rubiconGreen }} edge="start" onClick={() => deleteHandler(row.id)}>
            <DeleteIcon />
          </IconButton>
        </TableCell>
      </TableRow>
    </React.Fragment>
  );
}

interface RetirementCompositionTableProps {
  rows: TransactionProjectVintage[];
  bookId: uuid;
  transactionId: uuid;
  sourceId: uuid;
  onRefresh: () => Promise<AdminRetirementResponse>;
}

export default function RetirementCompositionTable(props: RetirementCompositionTableProps): JSX.Element {
  const { rows, bookId, transactionId, sourceId, onRefresh } = props;
  const [editIdx, setEditIdx] = useState<uuid>();
  const [showNewRow, setShowNewRow] = useState<boolean>(false);
  const [vintages, setVintages] = useState<ProjectVintageTableRow[]>();
  const [deletedVintages, setDeletedVintages] = useState<ProjectVintageTableRow[]>([]);
  const [sumOriginalAmount, setSumOriginalAmount] = useState<number>(0);
  const [sumCurrentAmount, setSumCurrentAmount] = useState<number>(0);
  const router = useRouter();
  const [isConfirmationDialogOpen, setIsConfirmationDialogOpen] = useState<boolean>(false);

  const [allAvailableProjects, setAllAvailableProjects] = useState<Project[]>();
  const [filteredProjects, setFilteredProjects] = useState<Project[]>();
  const [allAvailableVintages, setAllAvailableVintages] = useState<ExtendedTrimmedProjectVintageResponse[]>();

  const { enqueueSuccess, enqueueError } = useSnackbarVariants();
  const [compositionList, setCompositionList] = useState<RetirementCompositionVintage[]>();
  const [vintageQuantity, setVintageQuantity] = useState<IQuantity>({
    value: 0,
    error: false,
    message: "",
  });

  const [selectedProject, setSelectedProject] = useState<IProject>({
    value: null,
    error: false,
    message: "please select a project",
  });

  const [selectedVintage, setSelectedVintage] = useState<IVintage>({
    value: null,
    error: false,
    message: "",
  });

  const { data: bookResponse, error: bookResponseError } = useSWR<AdminBookResponse>(
    bookId
      ? `/admin/books/${bookId}?includeRelations=${BookRelations.OWNER_ALLOCATIONS_NESTED}&includeRelations=${BookRelations.OWNER_ALLOCATIONS_BY_PROJECT_TYPE}`
      : null,
  );

  const getAvailableVintages = useCallback(
    (projectId: uuid) => {
      //Getting the vintages list from the book and removing the ones that are already in the table
      const filteredVintages: AdminAssetResponse[] = (
        bookResponse?.ownerAllocations as AdminGroupedAllocationWithNestedResponse
      )?.allocations
        .map((bc) => bc?.asset)
        .filter((pv: AdminAssetResponse) => pv?.projectId === projectId && !vintages.some((v) => v.id === pv.id))
        .sort((a, b) =>
          +a.projectVintageName < +b.projectVintageName ? -1 : +a.projectVintageName > +b.projectVintageName ? 1 : 0,
        );

      const availableVintages: ExtendedTrimmedProjectVintageResponse[] = [];
      filteredVintages.forEach((v: AdminAssetResponse) => {
        const allocationComponent = (
          bookResponse?.ownerAllocations as AdminGroupedAllocationWithNestedResponse
        )?.allocations.find((e) => e.asset.id === v.id);
        const transactedVintage: TransactionProjectVintage = rows?.find((tv) => tv.id === v.id);
        availableVintages.push({
          ...v,
          amountAvailable:
            (allocationComponent?.amountAvailable ?? 0) +
            (transactedVintage?.amountTransacted ?? 0) /* todo : @kofi to double check this */,
        });
      });

      setAllAvailableVintages(availableVintages);
    },
    [vintages, bookResponse, rows],
  );

  const vintageUploadHandler = useCallback((compositionData: ProjectVintageTableRow[]) => {
    setVintages(compositionData);
  }, []);

  useEffect(() => {
    if (rows) {
      setVintages(rows);
      const result = calcOriginalAmountSum(rows);
      setSumOriginalAmount(result);
    }
  }, [rows]);

  //Filling project type from the book response
  useEffect(() => {
    if (!bookResponseError && !!vintages && !!bookResponse) {
      for (const vintage of vintages) {
        const { asset } =
          (bookResponse?.ownerAllocations as AdminGroupedAllocationWithNestedResponse)?.allocations?.find(
            (p) => p.asset.id === vintage.id,
          ) || {};
        vintage.projectType = asset?.projectTypeType;
      }
    }
  }, [vintages, bookResponse, bookResponseError]);

  useEffect(() => {
    if (selectedProject.value) {
      getAvailableVintages(selectedProject.value.id);
    }
  }, [selectedProject, getAvailableVintages, bookResponse]);

  useEffect(() => {
    setCompositionList(updateChanges(rows, vintages ?? []));
    if (vintages) {
      setSumCurrentAmount(calcOriginalAmountSum(vintages));
    }
  }, [rows, vintages]);

  useEffect(() => {
    if (vintages) {
      vintages.sort((a, b) =>
        alphabeticalThenNumberSort(
          { x: a.projectName ?? "", y: +(a.vintageName ?? "0") },
          { x: b.projectName ?? "", y: +(b.vintageName ?? "0") },
        ),
      );
    }
  }, [vintages]);

  const injectAmountUnallocated = useCallback(() => {
    if (bookResponse && !isEmpty(rows)) {
      rows.forEach((row) => {
        const component = (
          bookResponse?.ownerAllocations as AdminGroupedAllocationWithNestedResponse
        )?.allocations?.find((e) => e.asset.id === row.id);
        row.amountUnallocated =
          (component?.amountAvailable ?? 0) + row.amountTransacted; /* todo : @kofi to double check this */
        return row;
      });
    }
  }, [bookResponse, rows]);

  useEffect(() => {
    if (bookResponse) {
      const retirementVintages = vintages?.map(({ id }) => id);
      const allProjects = mapProject(
        // @kofi can we use ownerAllocationsByProject here?
        (bookResponse?.ownerAllocations as AdminGroupedAllocationWithNestedResponse)?.allocations
          ?.filter((v) => !retirementVintages?.includes(v.asset.id))
          .map((component: AdminAllocationResponse) => component.asset),
      );

      const uniqueProjects = uniqueByKeys(allProjects, ["id"]).sort((a, b) => a.name.localeCompare(b.name));
      setAllAvailableProjects(uniqueProjects);
      setFilteredProjects(uniqueProjects);
      injectAmountUnallocated();
    }
  }, [vintages, bookResponse, injectAmountUnallocated]);

  const updateChanges = (
    rowsArray: TransactionProjectVintage[],
    vintagesArray: TransactionProjectVintage[],
  ): CompositionVintage[] => {
    const updatesList: CompositionVintage[] = [];

    if (rowsArray && vintagesArray) {
      rowsArray.forEach((row) => {
        const vintage = vintagesArray.find((v) => v.id === row.id);
        let quantity = 0;

        if (!vintage) {
          updatesList.push({
            id: row.id,
            quantity: 0,
            name: buildUpdatedRowName(row),
            originalQuantity: row.amountTransacted,
          });
        } else {
          if (vintage.amountTransacted !== row.amountTransacted) {
            quantity = vintage.amountTransacted;
            updatesList.push({
              id: row.id,
              quantity,
              name: buildUpdatedRowName(row),
              originalQuantity: row.amountTransacted,
            });
          }
        }
      });

      vintagesArray.forEach((vintage) => {
        const row = rowsArray.find((r) => r.id === vintage.id);
        if (!row) {
          updatesList.push({
            id: vintage.id,
            quantity: vintage.amountTransacted,
            name: buildUpdatedRowName(vintage),
            originalQuantity: 0,
          });
        }
      });
    }

    return updatesList;
  };

  const validateQuantity = (
    quantity: Nullable<number>,
    amountUnallocated: number,
    deletedAmountTransacted: number,
  ): IQuantity => {
    if (!isNothing(quantity)) {
      if (quantity === 0)
        return {
          value: quantity,
          error: true,
          message: "Quantity must be greater than 0",
        };

      if (quantity! > sumOriginalAmount) {
        return {
          value: quantity,
          error: true,
          message: `Quantity must be equal to or less than ${integerFormat(sumOriginalAmount)}`,
        };
      }

      if (quantity! > amountUnallocated) {
        return {
          value: quantity,
          error: true,
          message: `Quantity must be equal to or less than amount available (${integerFormat(amountUnallocated)})`,
        };
      }
    }

    return {
      value: quantity,
      error: false,
      message: `${integerFormat(amountUnallocated ? deletedAmountTransacted + amountUnallocated : null)} available`,
    };
  };

  const startEditingHandler = (idx: uuid): void => {
    setEditIdx(idx);
  };

  const stopEditingHandler = (): void => {
    setEditIdx(undefined);
  };

  const deleteHandler = (selectedVintage: uuid): void => {
    const removed = vintages?.find((vintage) => vintage.id === selectedVintage);
    const filteredVintages = vintages?.filter((vintage) => vintage.id !== selectedVintage);

    if (removed) setDeletedVintages((vintages) => [...vintages, removed]);
    if (removed) setDeletedVintages((vintages) => [...vintages, removed]);

    setVintages(filteredVintages);
  };

  const currencyOnChangeHandler = (value: number, id: uuid): void => {
    const updatedRows = vintages?.map((vintage) => {
      return vintage.id === id ? { ...vintage, amountTransacted: value } : vintage;
    });
    setVintages(updatedRows);
  };

  const onSearchChangeHandler = (value: string): void => {
    getFilteredData(value);
  };

  const getFilteredData = (input: string): void => {
    const searchString = input.toUpperCase();
    const allVintages = vintages?.map((vintage) => {
      if (isRowContainsValue(vintage, searchString)) {
        vintage.isHide = false;
      } else {
        vintage.isHide = true;
      }
      return vintage;
    });

    setVintages(allVintages);
  };

  const onSubmit = (): void => {
    setIsConfirmationDialogOpen(true);
  };

  const newVintageQuantityHandler = (newQuantity: string): void => {
    if (selectedVintage.value) {
      const { amountTransacted = 0 } = deletedVintages?.find(({ id }) => id === selectedVintage.value?.id) || {};
      const value = convertAmountToNumber(newQuantity);
      const validatedQuantity = validateQuantity(value, selectedVintage.value.amountAvailable, amountTransacted);
      setVintageQuantity(validatedQuantity);
    }
  };

  const addVintageHandler = (): void => {
    setShowNewRow(true);
  };

  const resetHandler = (): void => {
    setVintages(rows);
    setDeletedVintages([]);
    setEditIdx(undefined);
    setShowNewRow(false);
    resetNewVintageFields();
  };

  const projectsDefaultProps = {
    options: filteredProjects ?? [],
    getOptionLabel: (option: Project): string => `${option.registry} - ${option.name}`,
    isOptionEqualToValue: (option: Project, value: Project): boolean =>
      option.registry === value.registry || option.name === value.name,
  };

  const projectTypesDefaultProps = {
    options: uniqueByKeys(filteredProjects, ["type"]),
    getOptionLabel: (option: Project): string => option?.type ?? "",
    isOptionEqualToValue: (option: Project, value: Project): boolean => option.type === value.type,
  };

  const locationsDefaultProps = {
    options: uniqueByKeys(filteredProjects, ["location"]),
    getOptionLabel: (option: Project): string => option?.location ?? "",
    isOptionEqualToValue: (option: Project, value: Project): boolean => option.location === value.location,
  };

  const vintagesDefaultProps = {
    options: uniqueByKeys(allAvailableVintages, ["name"]),
    getOptionLabel: (option: AdminAssetResponse): string => option?.name ?? "",
    isOptionEqualToValue: (option: AdminAssetResponse, value: AdminAssetResponse): boolean =>
      option.name === value.name,
  };

  const handleProjectSelection = (
    event: SyntheticEvent,
    value: Nullable<Project>,
    filterBy: "name" | "type" | "location" | "" = "",
  ): void => {
    event.preventDefault();
    const { name: valueName, type: valueType, location: valueLocation } = value || {};

    switch (filterBy) {
      case "name":
        setFilteredProjects(allAvailableProjects?.filter(({ name }) => (valueName ?? name) === name));
        break;
      case "type":
        setFilteredProjects(allAvailableProjects?.filter(({ type }) => (valueType ?? type) === type));
        break;
      case "location":
        setFilteredProjects(allAvailableProjects?.filter(({ location }) => (valueLocation ?? location) === location));
        break;
      default:
        setFilteredProjects(allAvailableProjects);
    }

    setSelectedProject({
      value,
      error: false,
      message: "",
    });

    if (!value) {
      setVintageQuantity({
        value: 0,
        error: false,
        message: "",
      });

      setSelectedVintage({
        value: null,
        error: false,
        message: "",
      });
    }
  };

  const inferredProjectAutoCompleteValue = (of: "name" | "type" | "location"): MaybeNothing<Project> => {
    if (selectedProject?.value) {
      const uniqueProjects = uniqueByKeys(filteredProjects, [of]);
      return uniqueProjects.length === 1 ? uniqueProjects[0] : null;
    }
    return null;
  };

  const vintageSelectionHandler = (
    event: SyntheticEvent,
    newValue: ExtendedTrimmedProjectVintageResponse | null,
  ): void => {
    setSelectedVintage({
      value: newValue,
      error: false,
      message: "",
    });

    setVintageQuantity({
      value: 0,
      error: false,
      message: `${integerFormat(newValue?.amountAvailable)} available`,
    });
  };

  const isNewVintageValid = (): boolean => {
    if (selectedProject.value === null) {
      setSelectedProject({
        ...selectedProject,
        error: true,
        message: "please select a project",
      });

      return false;
    }

    if (selectedVintage.value === null) {
      setSelectedVintage({
        ...selectedVintage,
        error: true,
        message: "please select a vintage",
      });

      return false;
    }

    const { amountTransacted = 0 } = deletedVintages?.find(({ id }) => id === selectedVintage.value?.id) || {};

    const validatedQuantity = validateQuantity(
      vintageQuantity.value,
      selectedVintage.value.amountAvailable,
      amountTransacted,
    );

    if (validatedQuantity.error) {
      setVintageQuantity(validatedQuantity);
      return false;
    }

    return true;
  };

  const addNewVintageHandler = (): void => {
    if (!isNewVintageValid()) return;

    const newProjectVintage: ProjectVintageTableRow = {
      id: selectedVintage?.value?.id,
      projectName: selectedProject?.value?.name,
      registryProjectId: selectedProject?.value?.registry,
      projectType: selectedProject?.value?.type,
      location: selectedProject?.value?.location,
      vintageName: selectedVintage?.value?.projectVintageName,
      interval: selectedVintage?.value?.projectVintageName,
      buffer: !isNil(selectedVintage?.value?.riskBufferPercentage) ? +selectedVintage?.value?.riskBufferPercentage : undefined, // todo : check if these are needed
      // costBasis: +selectedVintage.value.averageCostBasis,
      amountTransacted: vintageQuantity.value ?? 0,
      amountUnallocated: selectedVintage?.value?.amountAvailable,
      suspended: selectedProject?.value?.suspended,
      rctStandard: selectedProject?.value?.rctStandard,
      isScienceTeamApproved: selectedProject?.value?.isScienceTeamApproved,
    };

    const newVintages = [...(vintages ?? [])];
    newVintages.push(newProjectVintage);

    setVintages(newVintages);
    resetNewVintageFields();
  };

  const resetNewVintageFields = (): void => {
    setSelectedVintage({
      value: null,
      error: false,
      message: "",
    });

    setVintageQuantity({
      value: 0,
      error: false,
      message: "",
    });

    setSelectedProject({
      value: null,
      error: false,
      message: "please select a project",
    });

    setFilteredProjects(allAvailableProjects);

    setShowNewRow(false);
  };

  const deleteNewVintageHandler = (): void => {
    resetNewVintageFields();
  };

  const refreshPage = (): void => {
    onRefresh();
  };

  function closeConfirmationHandler(): void {
    setIsConfirmationDialogOpen(false);
  }

  function onSuccessHandler(message: string): void {
    setCompositionList(undefined);
    enqueueSuccess(message);
    router.back();
  }

  const columns = ["", "Project", "Project type", "Location", "Vintage", "Quantity"];

  const allocationEligibility = useMemo(
    () =>
      !!selectedVintage?.value === null || selectedVintage?.value === undefined
        ? null
        : selectedVintage?.value?.isRctEligible,
    [selectedVintage?.value],
  );

  return (
    <TableBox>
      <Paper sx={{ width: "100%", overflow: "hidden" }}>
        <TableContainer sx={{ maxHeight: 800 }}>
          <SearchAppBar onChangeHandler={onSearchChangeHandler}>
            <Stack direction="row" gap={0.5}>
              <Box>
                <ActionButton
                  isDisabled={isEmpty(compositionList) || sumOriginalAmount !== sumCurrentAmount}
                  onClickHandler={onSubmit}
                  requiredPermission={PermissionEnum.RETIREMENTS_CLEAR_PM_REVIEW}
                >
                  Save
                </ActionButton>
              </Box>
              <Box>
                <ActionButton
                  onClickHandler={addVintageHandler}
                  startIcon={<AddIcon />}
                  requiredPermission={PermissionEnum.RETIREMENTS_CLEAR_PM_REVIEW}
                  isDisabled={filteredProjects?.length === 0}
                  style={{ width: "160px" }}
                >
                  Add Vintage
                </ActionButton>
              </Box>
              <Box>
                <RetirementCompositionUpload
                  sumOriginalAmount={sumOriginalAmount}
                  vintageAllocations={
                    (bookResponse?.ownerAllocations as AdminGroupedAllocationWithNestedResponse)?.allocations
                  }
                  onVintagesUpload={vintageUploadHandler}
                />
              </Box>
              <Box>
                <ActionButton
                  onClickHandler={resetHandler}
                  startIcon={<UndoIcon />}
                  requiredPermission={PermissionEnum.RETIREMENTS_CLEAR_PM_REVIEW}
                >
                  RESET
                </ActionButton>
              </Box>
            </Stack>
          </SearchAppBar>
          <Table aria-label="composition table" stickyHeader>
            <TableHead sx={{ backgroundColor: COLORS.tableHeader }}>
              <TableRow>
                {columns?.map((column, idx) => (
                  <TableCell key={`${column}-${idx}`}>
                    <Typography variant="body2" component="h4" fontWeight="700">
                      {column}
                    </Typography>
                  </TableCell>
                ))}
                <TableCell />
                <TableCell />
              </TableRow>
            </TableHead>
            <TableBody>
              {showNewRow && (
                <TableRow sx={newRowStyle}>
                  <TableCell align="left">
                    <Typography variant="body2" component="div" sx={itemStyle}>
                      {allocationEligibility === null ? (
                        MISSING_DATA
                      ) : (
                        <RCTEligibilityChip vintage={selectedVintage?.value as any} />
                      )}
                    </Typography>
                  </TableCell>
                  <TableCell align="left">
                    <Autocomplete
                      {...projectsDefaultProps}
                      onChange={(event:SyntheticEvent, value: Nullable<Project>) =>
                        handleProjectSelection(event, value, "name")
                      }
                      value={inferredProjectAutoCompleteValue("name")}
                      loading
                      disablePortal
                      id="project"
                      renderInput={(params) => (
                        <TextField
                          error={selectedProject.error}
                          helperText={selectedProject.message}
                          required
                          {...params}
                          size="small"
                          InputProps={{
                            ...params.InputProps,
                            style: { fontSize: 14 },
                          }}
                        />
                      )}
                    />
                  </TableCell>
                  <TableCell align="left">
                    <Autocomplete
                      {...projectTypesDefaultProps}
                      onChange={(event: SyntheticEvent, value: Nullable<Project>) =>
                        handleProjectSelection(event, value, "type")
                      }
                      value={inferredProjectAutoCompleteValue("type")}
                      loading
                      disablePortal
                      id="project-type"
                      renderInput={(params) => (
                        <TextField
                          error={selectedProject.error}
                          required
                          {...params}
                          size="small"
                          InputProps={{
                            ...params.InputProps,
                            style: { fontSize: 14 },
                          }}
                        />
                      )}
                    />
                  </TableCell>
                  <TableCell align="left">
                    <Autocomplete
                      {...locationsDefaultProps}
                      onChange={(event: SyntheticEvent, value: Nullable<Project>) =>
                        handleProjectSelection(event, value, "location")
                      }
                      value={inferredProjectAutoCompleteValue("location")}
                      loading
                      disablePortal
                      id="location"
                      renderInput={(params) => (
                        <TextField
                          error={selectedProject.error}
                          required
                          {...params}
                          size="small"
                          InputProps={{
                            ...params.InputProps,
                            style: { fontSize: 14 },
                          }}
                        />
                      )}
                    />
                  </TableCell>
                  {/*  */}
                  <TableCell align="left">
                    <Autocomplete
                      {...vintagesDefaultProps}
                      disabled={isNil(selectedProject.value)}
                      onChange={vintageSelectionHandler}
                      value={selectedVintage.value}
                      disablePortal
                      id="vintage"
                      renderInput={(params) => (
                        <TextField
                          error={selectedVintage.error}
                          helperText={selectedVintage.message}
                          required
                          {...params}
                          size="small"
                          InputProps={{
                            ...params.InputProps,
                            style: { fontSize: 14 },
                          }}
                        />
                      )}
                    />
                  </TableCell>
                  <TableCell align="left">
                    <NumericFormat
                      disabled={isNil(selectedVintage.value)}
                      size="small"
                      defaultValue={0}
                      value={vintageQuantity.value}
                      decimalScale={0}
                      inputProps={{ maxLength: 20, style: { fontSize: 14 } }}
                      allowNegative={false}
                      customInput={TextField}
                      type="text"
                      thousandSeparator={","}
                      helperText={vintageQuantity.message}
                      error={vintageQuantity.error}
                      onChange={(e) => newVintageQuantityHandler(e.target.value)}
                    />
                  </TableCell>
                  <TableCell align="left">
                    <IconButton
                      sx={{ color: COLORS.rubiconGreen }}
                      edge="start"
                      onClick={addNewVintageHandler}
                      disabled={vintageQuantity.error}
                    >
                      <CheckIcon />
                    </IconButton>
                  </TableCell>
                  <TableCell align="left">
                    <IconButton sx={{ color: COLORS.rubiconGreen }} edge="start" onClick={deleteNewVintageHandler}>
                      <DeleteIcon />
                    </IconButton>
                  </TableCell>
                </TableRow>
              )}
              {vintages?.map(
                (row) =>
                  !row.isHide && (
                    <Row
                      key={row.id}
                      row={row}
                      idx={row.id}
                      editIdx={editIdx}
                      deletedVintages={deletedVintages}
                      startEditing={startEditingHandler}
                      stopEditing={stopEditingHandler}
                      deleteHandler={deleteHandler}
                      currencyOnChangeHandler={currencyOnChangeHandler}
                      validateQuantity={validateQuantity}
                    />
                  ),
              )}
            </TableBody>
          </Table>
        </TableContainer>
        {!isEmpty(vintages) && (
          <Box sx={{ height: "50px" }}>
            <Typography
              variant="body2"
              component="p"
              sx={{
                marginTop: "15px",
                marginLeft: "15px",
                textAlign: "left",
                fontWeight: "600",
              }}
            >
              {`Total (Current / Original): ${integerFormat(sumCurrentAmount)} / ${integerFormat(sumOriginalAmount)}`}
            </Typography>
            {sumOriginalAmount !== sumCurrentAmount && (
              <Typography
                variant="body2"
                component="p"
                sx={{
                  marginLeft: "15px",
                  marginTop: "5px",
                  textAlign: "left",
                  color: COLORS.red,
                  fontSize: "12px",
                }}
              >
                {`Total quantity should be equal to ${integerFormat(sumOriginalAmount)}`}
              </Typography>
            )}
            <Maybe condition={filteredProjects?.length === 0}>
              <Typography
                variant="body2"
                component="p"
                sx={{
                  marginLeft: "15px",
                  marginTop: "5px",
                  textAlign: "left",
                  color: COLORS.black,
                  fontSize: "12px",
                  fontWeight: "bold",
                }}
              >
                No more vintages left to add
              </Typography>
            </Maybe>
          </Box>
        )}
        {isEmpty(vintages) && !showNewRow && (
          <Box sx={{ height: "50px" }}>
            <Typography variant="body2" component="p" sx={{ marginTop: "30px", textAlign: "center" }}>
              Click <b>ADD VINTAGE</b>
            </Typography>
          </Box>
        )}
      </Paper>
      <Box>
        <RetirementCompositionModal
          isOpen={isConfirmationDialogOpen}
          transactionId={transactionId}
          onClose={closeConfirmationHandler}
          onConfirm={onSuccessHandler}
          onError={enqueueError}
          retirementComposition={vintages ?? []}
          changesList={compositionList ?? []}
          refreshData={refreshPage}
          sourceId={sourceId}
        />
      </Box>
    </TableBox>
  );
}
