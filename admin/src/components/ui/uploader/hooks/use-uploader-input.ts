import { useRef, useEffect, RefObject } from "react";
import { recordIsEmpty } from "../utils/widget";
import { FileDataRecord } from "../types/hook";

const useUploaderInput = (records: FileDataRecord): RefObject<HTMLInputElement> => {
  const inputRef = useRef<HTMLInputElement>(undefined);

  useEffect(() => {
    if (recordIsEmpty(records) && inputRef.current) inputRef.current.value = "";
  }, [records, inputRef]);

  return inputRef as RefObject<HTMLInputElement>;
};

export default useUploaderInput;
