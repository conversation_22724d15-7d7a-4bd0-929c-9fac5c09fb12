import { Switch, SwitchProps } from "@mui/material";
import { classcat } from "@rubiconcarbon/frontend-shared";

import type { JSX } from "react";

import classes from "./style.module.scss";

const Toggle = ({ className, size, ...props }: SwitchProps): JSX.Element => {
  return (
    <Switch
      className={classcat([classes.Toggle, className])}
      classes={{
        root: classes.Root,
        switchBase: classes.SwitchBase,
        thumb: classes.Thumb,
        track: classes.Track,
        checked: classes.Checked,
        disabled: classes.Disabled,
      }}
      data-size={size}
      disableRipple
      {...props}
    />
  );
};

export default Toggle;
