import { AuthorizeServer } from "@app/authorize-server";
import { PermissionEnum, AdminRetirementResponse, RetirementRelations } from "@rubiconcarbon/shared-types";
import RetirementItem from "./components";
import { baseApiRequest, generateQueryParams } from "@app/libs/server";
import { withErrorHandling } from "@app/data-server";
import { isValidElement, type JSX } from "react";

/**
 * Retirement Item Page
 *
 * This is a server component that renders the Retirement Item page
 */
export default async function RetirementItemPage({
  params,
}: {
  params: Promise<{ "retirement-id": string }>;
}): Promise<JSX.Element> {
  const { "retirement-id": id } = await params;

  const retirementResponse = await withErrorHandling(async () =>
    baseApiRequest<AdminRetirementResponse>(
      `admin/retirements/${id}?${generateQueryParams({
        includeRelations: [
          RetirementRelations.CUSTOMER_PORTFOLIO,
          RetirementRelations.ASSETS,
          RetirementRelations.RCT_VINTAGES,
          RetirementRelations.LINKS,
          RetirementRelations.REQUESTED_BY,
        ],
      })}`,
    ),
  );

  // Check if the result is a server error
  if (isValidElement(retirementResponse)) return retirementResponse;

  return (
    <AuthorizeServer permissions={[PermissionEnum.RETIREMENTS_READ]}>
      <RetirementItem retirementResponse={retirementResponse as AdminRetirementResponse} />
    </AuthorizeServer>
  );
}
