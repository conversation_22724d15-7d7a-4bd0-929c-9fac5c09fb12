/* telemetry */
import { initTelemetry } from './telemetry';
initTelemetry();
/* nestjs */
import { HttpAdapterHost, NestFactory } from '@nestjs/core';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import { ValidationPipe, VersioningType } from '@nestjs/common';
import { NestExpressApplication } from '@nestjs/platform-express';
/* express */
import { json } from 'express';
/* other */
import { IncomingMessage, ServerResponse } from 'http';
import { join } from 'path';
import * as promBundle from 'express-prom-bundle'; // eslint-disable-line import/no-namespace
/* config */
import { environment } from '@env/environment';
/* app */
import { AllExceptionsFilter } from './helpers/exception-filter.helper';
import { AppModule } from './app.module';
import { Logger } from 'nestjs-pino';

const rawBodyBuffer = (req: IncomingMessage, _: ServerResponse, buf: Buffer, encoding: string): void => {
  if (!req.headers['apisign']) return;
  (req as any).rawBody = buf && buf.length ? buf.toString((encoding as BufferEncoding) || 'utf8') : '';
};

async function bootstrap(): Promise<void> {
  const app = await NestFactory.create<NestExpressApplication>(AppModule, {
    ...environment.app.parameters,
    bufferLogs: true,
  });

  // metrics
  const metricsMiddleware = promBundle({
    includeMethod: true,
    includePath: true,
    includeStatusCode: true,
    promClient: {
      collectDefaultMetrics: {},
    },
    autoregister: false,
    excludeRoutes: [
      `/${environment.app.prefix}/healthz`.replace(/\/+/g, '/'),
      `/${environment.app.prefix}/metrics`.replace(/\/+/g, '/'),
      RegExp(`^/?${environment.app.prefix}/docs.*`),
    ],
  });
  app.use(metricsMiddleware);

  // install under /api
  app.setGlobalPrefix(environment.app.prefix, { exclude: ['signin-oidc', 'oauth2-redirect.html'] });

  // api versioning
  app.enableVersioning({
    type: VersioningType.URI,
  });

  // add a raw body value to requests to allow signing of requests
  // app.use(express.urlencoded({ verify: rawBodyBuffer, extended: true }));
  app.use(json({ verify: rawBodyBuffer, limit: '1mb' }));

  // (eventually) enable cors
  app.enableCors();

  // validation
  app.useGlobalPipes(
    new ValidationPipe({
      transform: true,
      whitelist: true,
      transformOptions: { exposeUnsetFields: false },
      forbidUnknownValues: false,
    }),
  );

  // install exception filters
  app.useGlobalFilters(new AllExceptionsFilter(app.get(HttpAdapterHost), app.get(Logger)));

  // swagger
  const configSwagger = new DocumentBuilder()
    .setTitle('Rubicon API')
    .setDescription('Rubicon API')
    .setVersion('v1')
    .addBearerAuth(undefined, 'jwt')
    .build();

  SwaggerModule.setup('api/docs', app, SwaggerModule.createDocument(app, configSwagger), {
    swaggerOptions: {
      defaultModelsExpandDepth: -1,
      defaultModelExpandDepth: 2,
      persistAuthorization: true,
    },
    customJs: 'https://ajax.googleapis.com/ajax/libs/jquery/3.6.4/jquery.min.js',
    customJsStr: `
      var waitForEl = function(selector, callback) {
        if (jQuery(selector).length) {
          callback();
        } else {
          setTimeout(function() {
            waitForEl(selector, callback);
          }, 100);
        }
      };

      waitForEl("button.authorize", function() {
        let adminToken = localStorage.getItem('platform-tkn');
        let webToken = localStorage.getItem('auth-token');

        if(adminToken) {
          adminToken = JSON.parse(atob(adminToken));
          $(".auth-wrapper").prepend(
            '<button id="from-admin" class="btn authorize"><span style="padding: 0">Authorize from Admin</span></button>'
          );
          $("#from-admin").on("click", function() {
            localStorage.setItem('authorized', JSON.stringify({"jwt":{"name":"jwt","schema":{"scheme":"bearer","bearerFormat":"JWT","type":"http"},"value":adminToken}}));
            location.reload();
          });
        }

        if(webToken) {
          webToken = JSON.parse(atob(webToken))['token'];
          $(".auth-wrapper").prepend(
            '<button id="from-admin" class="btn authorize"><span style="padding: 0">Authorize from Web</span></button>'
          );
          $("#from-admin").on("click", function() {
            localStorage.setItem('authorized', JSON.stringify({"jwt":{"name":"jwt","schema":{"scheme":"bearer","bearerFormat":"JWT","type":"http"},"value":webToken}}));
            location.reload();
          });
        }
      });
    `,
  });

  // views for standard MVC logic
  app.setBaseViewsDir(join(__dirname, '..', 'views'));
  app.setViewEngine('hbs');

  // Starts listening for shutdown hooks
  app.enableShutdownHooks();

  await app.listen(environment.port, environment.host);
}
bootstrap();

/* istanbul ignore file */
