import { CSSProperties, ReactNode, RefObject, useMemo, type JSX } from "react";
import { Controller, FieldErrors, Path, FieldValues } from "react-hook-form";
import { ArrowDropDown, InfoRounded } from "@mui/icons-material";
import {
  TextField,
  Tooltip,
  MenuItem,
  Autocomplete,
  Stack,
  Box,
  ClickAwayListener,
  Typography,
  Button,
  Popper,
  Fade,
  Paper,
  FilterOptionsState,
} from "@mui/material";
import { DatePicker } from "@mui/x-date-pickers";
import {
  px,
  Maybe,
  pickFromRecord,
  AllKeys,
  usePopperState,
  useIsInView,
  NO_OP,
  isNothing,
  Undefinable,
  Nullable,
} from "@rubiconcarbon/frontend-shared";
import { NumericFormat, NumberFormatValues } from "react-number-format";
import { GenericTableSimpleType } from "../types/generic-table-simple-type";
import { GenericTableRowModel } from "../types/generic-table-row-model";
import { useMeasure } from "react-use";
import { UseMeasureRef } from "react-use/lib/useMeasure";
import { GenericTableFormHelperTextProps } from "../types/generic-table-form-helper-text";
import { GenericTableColumnValueOption } from "../types/generic-table-column-value-options";
import { GenericTableColumn, GenericTableFixedAutoCompleteOptions } from "../types/generic-table-column";
import GenericTableAsyncAutoCompleteField from "./generic-table-async-autocomplete-field";
import { Atom, useAtomValue, useStore } from "jotai";
import { activeRowsAtom, baseColumnsAtom, externalAtom } from "../state";
import { GenericTableExternal } from "../types/generic-table-external";
import { Values } from "../hooks/use-generic-table-utility";
import dayjs, { Dayjs } from "dayjs";

import classes from "../styles/generic-table-editable-column.module.scss";

type HelperTextProps<M extends FieldValues> = {
  measureRef?: UseMeasureRef<Element>;
  field: Path<M>;
  value: any;
  row?: GenericTableRowModel<M>;
  style?: CSSProperties;
  form: {
    formRowIndex?: number;
    errors?: FieldErrors<Values<M>>;
    formHelperText?: GenericTableSimpleType | ((props: GenericTableFormHelperTextProps<M>) => JSX.Element);
  };
};

type EndAdornmentProps = {
  dataTooltipContent?: ReactNode;
};

type GenericTableEditableFieldProps<M extends FieldValues> = {
  field: Path<M>;
  row: GenericTableRowModel<M>;
};

type UseHelperTextHeightAdjustmentReturn = {
  ref: UseMeasureRef<Element>;
  height: number;
};

export const filterAutoCompleteOptions = (
  options: GenericTableColumnValueOption[],
  state: FilterOptionsState<GenericTableColumnValueOption>,
  fixedAutoCompleteOptions: Undefinable<GenericTableFixedAutoCompleteOptions>,
): GenericTableColumnValueOption[] => {
  let fixedOptions: GenericTableColumnValueOption[] = [];

  const filteredOptions = options.filter((option) =>
    option?.label?.toLowerCase().includes(state?.inputValue?.toLowerCase()),
  );

  if (fixedAutoCompleteOptions) {
    const { options, renderOptionOn = true, renderOptionAt = "before" } = fixedAutoCompleteOptions;

    fixedOptions = options.filter((option) =>
      typeof renderOptionOn === "function" ? renderOptionOn(option, filteredOptions) : renderOptionOn,
    );

    const before = fixedOptions.filter((option) =>
      typeof renderOptionAt === "function"
        ? renderOptionAt(option, filteredOptions) === "before"
        : renderOptionAt === "before",
    );
    const after = fixedOptions.filter((option) =>
      typeof renderOptionAt === "function"
        ? renderOptionAt(option, filteredOptions) === "after"
        : renderOptionAt === "after",
    );

    const indexed = fixedOptions.filter((option) =>
      typeof renderOptionAt === "function"
        ? !["before", "after"].includes(renderOptionAt(option, filteredOptions) as any)
        : !["before", "after"].includes(renderOptionAt as any),
    );

    let allOptions = [...before, ...filteredOptions, ...after];

    for (const option of indexed) {
      const index = (
        typeof renderOptionAt === "function" ? renderOptionAt(option, allOptions) : renderOptionAt
      ) as number;

      allOptions = [...allOptions.slice(0, index), option, ...allOptions.slice(index + 1)];
    }

    return allOptions;
  }

  return filteredOptions;
};

const useHelperTextHeightAdjustment = (): UseHelperTextHeightAdjustmentReturn => {
  const [ref, { height }] = useMeasure();

  return {
    ref,
    height,
  };
};

export const HelperText = <M extends FieldValues>({
  measureRef,
  value,
  field,
  row,
  style = {},
  form = {},
}: HelperTextProps<M>): JSX.Element => {
  const { formRowIndex = -1, errors, formHelperText } = form;
  const path = `${field}.message` as AllKeys<FieldErrors<M>>;
  const error = pickFromRecord(errors?.amends?.at?.(formRowIndex) as any, [path as Path<M>], true)?.[path as string];

  return (
    <Box ref={measureRef} style={style}>
      {formHelperText
        ? typeof formHelperText === "function"
          ? formHelperText({ value, row, errors })
          : formHelperText
        : error}
    </Box>
  );
};

const EndAdornment = ({ dataTooltipContent }: EndAdornmentProps): JSX.Element => (
  <Tooltip sx={{ m: "0 3px" }} title={dataTooltipContent}>
    <InfoRounded className={classes.Info} />
  </Tooltip>
);

const GenericTableEditableField = <M extends FieldValues>({
  field,
  row,
}: GenericTableEditableFieldProps<M>): Nullable<JSX.Element> => {
  const store = useStore();

  const activeRows = useAtomValue<GenericTableRowModel<M>[]>(activeRowsAtom, { store });
  const { useForm } = useAtomValue<GenericTableExternal<M>>(externalAtom, { store }) || {};
  const baseColumns = useAtomValue<GenericTableColumn<M>[]>(baseColumnsAtom as Atom<GenericTableColumn<M>[]>, {
    store,
  });

  const { ref: helperTextRef, height: helperTextTopPadding } = useHelperTextHeightAdjustment();
  const {
    ref: popperRef,
    popperId,
    popout,
    close: closePopper,
    toggle: togglePopper,
  } = usePopperState<HTMLButtonElement>({ id: field });
  const popperTriggerIsInView = useIsInView(popperRef as RefObject<Nullable<HTMLElement>>, { threshold: 1, rootMargin: "-50px" });

  const form = useForm?.();
  const { control, formState } = form || {};
  const { errors } = formState || {};

  const {
    type = "freetext",
    autofocus = false,
    placeholder = "",
    tabIndex = 1,
    disable,
    useRenderedAsEdit: URaE = false, // renamed to "URaE" as eslint thinks it is a react hook
    validator,
    valueOptions = [],
    fixedAutoCompleteOptions: faopts,
    dataTooltipContent,
    tooltip,
    formHelperText,
    autoCompleteLoading = false,
    loadingAutoCompleteText = "Loading...",
    noAutocompleteOption = {
      label: "No Options",
      displayLabel: "No Options",
    },
    renderDataCell,
  } = useMemo(() => baseColumns?.find(({ field: f }) => f === field), [baseColumns, field]) || {};

  const formRowIndex = useMemo(
    () => activeRows?.findIndex((activeRow) => activeRow?.id === row?.id),
    [activeRows, row?.id],
  );
  const formFieldPath = useMemo(
    () => (formRowIndex !== -1 ? `amends.${formRowIndex}.${field}` : ""),
    [field, formRowIndex],
  ) as Path<Values<M>>;

  const path = `${field}.message` as AllKeys<FieldErrors<M>>;
  const error =
    formRowIndex !== -1
      ? pickFromRecord(errors?.amends?.at?.(formRowIndex) as any, [path as Path<M>], true)?.[path as string]
      : null;
  const hasError = !!error;

  const autoFocus = useMemo(() => (typeof autofocus === "function" ? autofocus(row) : autofocus), [autofocus, row]);

  const usingRenderedAsEdit = useMemo(() => (typeof URaE === "function" ? URaE(row) : URaE), [URaE, row]);

  const selectOptions = useMemo(
    () => (typeof valueOptions === "function" ? valueOptions(row) : valueOptions) || [],
    [row, valueOptions],
  );

  const autoCompleteIsLoading = useMemo(
    () => (typeof autoCompleteLoading === "function" ? autoCompleteLoading(selectOptions, row) : autoCompleteLoading),
    [autoCompleteLoading, row, selectOptions],
  );

  const loadingText = useMemo(
    () =>
      autoCompleteIsLoading
        ? typeof loadingAutoCompleteText === "function"
          ? loadingAutoCompleteText(row)
          : loadingAutoCompleteText
        : null,
    [autoCompleteIsLoading, row, loadingAutoCompleteText],
  );

  const noOptions = useMemo(
    () => (typeof noAutocompleteOption === "function" ? noAutocompleteOption(row) : noAutocompleteOption),
    [row, noAutocompleteOption],
  ) as GenericTableColumnValueOption;

  const definitionTT = typeof dataTooltipContent === "function" ? dataTooltipContent(row) : dataTooltipContent;

  const tt = typeof tooltip === "function" ? tooltip(row) : tooltip;

  const fixedAutoCompleteOptions = typeof faopts === "function" ? faopts(row) : faopts;
  const fieldValue = pickFromRecord(row, [field as unknown as AllKeys<M>], true)?.[field as string];
  const disabled = typeof disable === "function" ? disable(row) : disable;

  if (usingRenderedAsEdit && !!renderDataCell)
    return (
      <Stack justifyContent="center">
        <Box sx={{ paddingTop: `${helperTextTopPadding}px` }}>{renderDataCell(row)}</Box>
        <HelperText
          measureRef={helperTextRef}
          field={field}
          value={""}
          row={row}
          form={{
            formRowIndex,
            formHelperText: formHelperText as any,
            errors,
          }}
          style={{
            fontSize: "0.75rem",
            fontWeight: 400,
            color: "#C90005",
            marginTop: 3,
            marginLeft: 14,
          }}
        />
      </Stack>
    );

  if (formRowIndex === -1) return <></>;

  switch (type) {
    case "freetext":
    case "textarea":
      return (
        <Tooltip title={tt}>
          <Box width="100%">
            <Controller
              control={control}
              name={formFieldPath}
              render={({ field: { ref, value, ...otherProps } }): JSX.Element => (
                <TextField
                  sx={{ paddingTop: `${helperTextTopPadding}px` }}
                  autoFocus={autoFocus}
                  tabIndex={tabIndex}
                  value={value || ""}
                  placeholder={placeholder}
                  InputProps={{
                    ref,
                    ...px({
                      endAdornment: !!definitionTT && <EndAdornment dataTooltipContent={definitionTT} />,
                    }),
                    classes: {
                      root: classes.InputRoot,
                      input: classes.Input,
                    },
                  }}
                  error={hasError}
                  FormHelperTextProps={{
                    component: "div",
                  }}
                  helperText={
                    <HelperText
                      measureRef={helperTextRef}
                      field={field}
                      value={value}
                      row={row}
                      form={{
                        formRowIndex,
                        formHelperText: formHelperText as any,
                        errors,
                      }}
                    />
                  }
                  {...otherProps}
                  fullWidth
                  disabled={disabled}
                  {...px(
                    {
                      multiline: type === "textarea",
                      minRows: type === "textarea" && 3,
                    },
                    [false],
                  )}
                />
              )}
            />
          </Box>
        </Tooltip>
      );
    // todo: need to add inline tooltip for date
    case "date":
      return (
        <Tooltip title={tt}>
          <Box width="100%">
            <Controller
              control={control}
              name={formFieldPath}
              render={({ field: { ref, value, onChange, ...otherProps } }): JSX.Element => {
                const {
                  disablePast: dP,
                  disableFuture: dF,
                  minDate: mnD,
                  maxDate: mxD,
                  shouldDisableDate: sDD,
                  shouldDisableMonth: sDM,
                  shouldDisableYear: sDY,
                } = validator || {};

                const disablePast = !isNothing(dP) ? (typeof dP === "function" ? dP(row) : dP) : false;
                const disableFuture = !isNothing(dF) ? (typeof dF === "function" ? dF(row) : dF) : false;
                const minDate = !isNothing(mnD) ? (typeof mnD === "function" ? mnD(row) : mnD) : false;
                const maxDate = !isNothing(mxD) ? (typeof mxD === "function" ? mxD(row) : mxD) : false;
                const shouldDisableDate = !isNothing(sDD) ? sDD!(row) : null;
                const shouldDisableMonth = !isNothing(sDM) ? sDM!(row) : null;
                const shouldDisableYear = !isNothing(sDY) ? sDY!(row) : null;

                return (
                  <DatePicker
                    format="MM/DD/YYYY"
                    value={value ? dayjs(value as any) : null}
                    {...px(
                      {
                        disablePast,
                        disableFuture,
                        minDate,
                        maxDate,
                        shouldDisableDate,
                        shouldDisableMonth,
                        shouldDisableYear,
                      },
                      [false, null, undefined],
                    )}
                    onChange={(newValue: Nullable<Dayjs>) => onChange(newValue?.toDate())}
                    slotProps={{
                      inputAdornment: {
                        position: "end",
                        sx: { pr: "10px", "> button": { width: 30, height: 30 } },
                      },
                      textField: {
                        sx: { paddingTop: `${helperTextTopPadding}px` },
                        fullWidth: true,
                        InputProps: {
                          inputRef: ref,
                          classes: {
                            root: classes.InputRoot,
                            input: classes.Input,
                          },
                        },
                        error: hasError,
                        FormHelperTextProps: {
                          component: "div",
                        },
                        helperText: (
                          <HelperText
                            measureRef={helperTextRef}
                            field={field}
                            value={value}
                            row={row}
                            form={{
                              formRowIndex,
                              formHelperText: formHelperText as any,
                              errors,
                            }}
                          />
                        ),
                        disabled,
                        ...otherProps,
                      },
                      popper: {
                        placement: "bottom",
                      },
                    }}
                  />
                );
              }}
            />
          </Box>
        </Tooltip>
      );
    case "number":
    case "money":
      return (
        <Tooltip title={tt}>
          <Box width="100%">
            <Controller
              control={control}
              name={formFieldPath}
              render={({ field: { ref, value, onChange, ...otherProps } }): JSX.Element => {
                const nv = typeof value === "string" || typeof value === "number" ? value : "";

                return (
                  <NumericFormat
                    sx={{ paddingTop: `${helperTextTopPadding}px` }}
                    allowLeadingZeros={false}
                    allowNegative={
                      validator?.allowNegative
                        ? typeof validator?.allowNegative === "function"
                          ? validator?.allowNegative(row)
                          : validator?.allowNegative
                        : false
                    }
                    {...px(
                      {
                        prefix: type === "money" && "$",
                        decimalScale: type === "number" ? 0 : 2,
                        fixedDecimalScale: type === "money",
                        isAllowed: (values: NumberFormatValues): boolean => {
                          const pattern = /^[\d.]+$/;
                          const { value, floatValue } = values;

                          if (value === "") return true;

                          const hasValidNumberPattern = pattern.test(value);

                          if (!!validator?.min || !!validator?.max) {
                            const { min, max } = validator;
                            const coalescedValue = floatValue || 0;

                            const minResolved = typeof min === "function" ? min(row) : min;
                            const maxResolved = typeof max === "function" ? max(row) : max;

                            const hasValidMinMaxConstraints =
                              !isNothing(min) && !isNothing(max)
                                ? minResolved! <= coalescedValue && coalescedValue <= maxResolved!
                                : minResolved
                                  ? minResolved <= coalescedValue
                                  : maxResolved
                                    ? coalescedValue <= maxResolved
                                    : true;

                            return hasValidNumberPattern && hasValidMinMaxConstraints;
                          }

                          return hasValidNumberPattern;
                        },
                      },
                      [false],
                    )}
                    step={type === "money" ? 0.01 : 1}
                    autoFocus={autoFocus}
                    tabIndex={tabIndex}
                    thousandSeparator
                    value={nv}
                    placeholder={placeholder}
                    onValueChange={({ floatValue }) => onChange(floatValue)}
                    customInput={TextField}
                    InputProps={{
                      ref,
                      ...px({
                        endAdornment: !!definitionTT && <EndAdornment dataTooltipContent={definitionTT} />,
                      }),
                      classes: {
                        root: classes.InputRoot,
                        input: classes.Input,
                      },
                    }}
                    error={hasError}
                    FormHelperTextProps={{
                      component: "div",
                    }}
                    helperText={
                      <HelperText
                        measureRef={helperTextRef}
                        field={field}
                        value={value}
                        row={row}
                        form={{
                          formRowIndex,
                          formHelperText: formHelperText as any,
                          errors,
                        }}
                      />
                    }
                    {...otherProps}
                    fullWidth
                    disabled={disabled}
                  />
                );
              }}
            />
          </Box>
        </Tooltip>
      );
    case "select":
      return (
        <Tooltip title={tt}>
          <Box width="100%">
            <Controller
              control={control}
              name={formFieldPath}
              render={({ field: { ref, value, ...otherProps } }): JSX.Element => (
                <TextField
                  sx={{ paddingTop: `${helperTextTopPadding}px` }}
                  autoFocus={autoFocus}
                  tabIndex={tabIndex}
                  select
                  value={value || ""}
                  placeholder={placeholder}
                  InputProps={{
                    ref,
                    ...px({
                      endAdornment: !!definitionTT && <EndAdornment dataTooltipContent={definitionTT} />,
                    }),
                    classes: {
                      root: classes.InputRoot,
                      input: classes.Input,
                    },
                  }}
                  SelectProps={{
                    classes: {
                      icon: definitionTT ? classes.SelectIcon : "",
                    },
                    MenuProps: {
                      slotProps: {
                        paper: {
                          sx: {
                            maxHeight: 300,
                          },
                        },
                      },
                    },
                  }}
                  error={hasError}
                  FormHelperTextProps={{
                    component: "div",
                  }}
                  helperText={
                    <HelperText
                      measureRef={helperTextRef}
                      field={field}
                      row={row}
                      value={value}
                      form={{
                        formRowIndex,
                        formHelperText: formHelperText as any,
                        errors,
                      }}
                    />
                  }
                  {...otherProps}
                  fullWidth
                  disabled={disabled}
                >
                  {selectOptions?.map((option) => (
                    <MenuItem
                      key={JSON.stringify(option.value)}
                      value={option.value as any}
                      disabled={option?.disabled}
                      {...px(
                        {
                          className: (option?.internal && option?.className) ?? "",
                          style: (option?.internal && option?.style) ?? {},
                          onClick: (option?.internal && option?.onClick) ?? NO_OP,
                          onMouseMove: (option?.internal && option?.onMouseMove) ?? NO_OP,
                          onTouchStart: (option?.internal && option?.onTouchStart) ?? NO_OP,
                        },
                        ["", false, null, undefined],
                      )}
                    >
                      {option?.displayLabel || option.label}
                    </MenuItem>
                  ))}
                </TextField>
              )}
            />
          </Box>
        </Tooltip>
      );
    case "autocomplete":
      return (
        <Tooltip title={tt}>
          <Box width="100%">
            <Controller
              control={control}
              name={formFieldPath}
              render={({ field: { ref, value, onChange, ...otherProps } }): JSX.Element => {
                return (
                  <Autocomplete
                    sx={{ paddingTop: `${helperTextTopPadding}px` }}
                    options={selectOptions}
                    value={selectOptions?.find(({ value: v }) => v === value) || null}
                    loading={autoCompleteIsLoading}
                    loadingText={loadingText}
                    noOptionsText={noOptions.displayLabel || noOptions?.label}
                    filterOptions={(
                      options: GenericTableColumnValueOption[],
                      state: FilterOptionsState<GenericTableColumnValueOption>,
                    ): GenericTableColumnValueOption[] =>
                      !autoCompleteIsLoading ? filterAutoCompleteOptions(options, state, fixedAutoCompleteOptions) : []
                    }
                    ListboxProps={{
                      style: {
                        maxHeight: 300,
                      },
                    }}
                    renderInput={({ InputProps, ...params }) => (
                      <TextField
                        autoFocus={autoFocus}
                        placeholder={placeholder}
                        tabIndex={tabIndex}
                        {...params}
                        InputProps={{
                          ...InputProps,
                          endAdornment: (
                            <Stack direction="row">
                              <Maybe condition={!!definitionTT}>
                                <Tooltip title={definitionTT}>
                                  <InfoRounded className={classes.Info} />
                                </Tooltip>
                              </Maybe>
                              {InputProps.endAdornment}
                            </Stack>
                          ),
                        }}
                        {...otherProps}
                        inputRef={ref}
                        error={hasError}
                        FormHelperTextProps={{
                          component: "div",
                        }}
                        helperText={
                          <HelperText
                            measureRef={helperTextRef}
                            field={field}
                            value={value}
                            row={row}
                            form={{
                              formRowIndex,
                              formHelperText: formHelperText as any,
                              errors,
                            }}
                          />
                        }
                        fullWidth
                        disabled={disabled}
                      />
                    )}
                    renderOption={(props, option) => (
                      <li
                        {...props}
                        key={`${option.value}-${option.label}-${option.displayLabel}`}
                        {...px(
                          {
                            className: option?.internal ? (option?.className ?? "") : null,
                            style: option?.internal ? (option?.style ?? {}) : null,
                            onClick: option?.internal ? (option?.onClick ?? NO_OP) : null,
                            onMouseMove: option?.internal ? (option?.onMouseMove ?? NO_OP) : null,
                            onTouchStart: option?.internal ? (option?.onTouchStart ?? NO_OP) : null,
                          },
                          [false, null, undefined],
                        )}
                      >
                        {option?.displayLabel || option.label}
                      </li>
                    )}
                    disabled={disabled}
                    onChange={(_, selection) => onChange(selection?.value)}
                    classes={{
                      root: classes.Autocomplete,
                      inputRoot: classes.InputRoot,
                      input: classes.Input,
                    }}
                  />
                );
              }}
            />
          </Box>
        </Tooltip>
      );
    case "async-autocomplete":
      return (
        <Tooltip title={tt}>
          <Box>
            <Controller
              control={control}
              name={formFieldPath}
              render={({ field: { ref, value, onChange, ...otherProps } }): JSX.Element => (
                <GenericTableAsyncAutoCompleteField
                  controllerRef={ref}
                  field={field}
                  disabled={!!disabled}
                  value={value}
                  row={row}
                  formRowIndex={formRowIndex}
                  errors={errors || {}}
                  columns={baseColumns}
                  helperTextTopPadding={helperTextTopPadding}
                  loading={autoCompleteIsLoading}
                  loadingText={loadingText}
                  noOptionsText={noOptions.displayLabel || noOptions?.label}
                  otherProps={otherProps}
                  fixedAutoCompleteOptions={fixedAutoCompleteOptions as any}
                  helperTextRef={helperTextRef}
                  onChange={onChange}
                />
              )}
            />
          </Box>
        </Tooltip>
      );
    case "text-popper":
      return (
        <Box sx={{ width: "100%", paddingTop: `${helperTextTopPadding}px` }}>
          <ClickAwayListener onClickAway={closePopper}>
            <Box>
              <Tooltip title={!popout && tt} disableHoverListener={disabled}>
                <Stack gap={0.5}>
                  <Button
                    ref={popperRef as any}
                    className={classes.DropDownButton}
                    variant="outlined"
                    endIcon={
                      <>
                        <ArrowDropDown
                          fontSize="large"
                          sx={{
                            color: disabled ? "rgba(248, 248, 248, 0.26)" : "black",
                            rotate: popout ? "180deg" : "0deg",
                          }}
                        />
                        <Maybe condition={!!definitionTT}>
                          <Tooltip title={definitionTT}>
                            <InfoRounded className={classes.Info} />
                          </Tooltip>
                        </Maybe>
                      </>
                    }
                    style={{
                      borderColor: hasError ? "#C90005" : "gray",
                    }}
                    disabled={disabled}
                    onClick={togglePopper}
                  >
                    <Typography variant="body2" width="100%" textAlign="left">
                      {fieldValue}
                    </Typography>
                  </Button>
                  <HelperText
                    measureRef={helperTextRef}
                    field={field}
                    value={fieldValue}
                    row={row}
                    style={{ color: hasError ? "#C90005" : "#094436", paddingLeft: 15 }}
                    form={{
                      formRowIndex,
                      formHelperText: formHelperText as any,
                      errors,
                    }}
                  />
                </Stack>
              </Tooltip>
              <Popper
                id={popperId}
                transition
                open={popout && popperTriggerIsInView}
                anchorEl={popperRef?.current}
                sx={{
                  overflow: "visible",
                  filter: "drop-shadow(0px 2px 8px rgba(0,0,0,0.32))",
                  zIndex: 10000,
                }}
              >
                {({ TransitionProps }) => (
                  <Fade {...TransitionProps} timeout={100}>
                    <Paper>
                      <Box minWidth={300} padding={`25px 15px ${hasError ? "5px" : "15px"} 15px`}>
                        <Controller
                          control={control}
                          name={formFieldPath}
                          render={({ field: { ref, value, ...otherProps } }): JSX.Element => (
                            <TextField
                              autoFocus={autoFocus}
                              tabIndex={tabIndex}
                              value={value || ""}
                              placeholder={placeholder}
                              InputProps={{
                                ref,
                                ...px({
                                  endAdornment: !!definitionTT && <EndAdornment dataTooltipContent={definitionTT} />,
                                }),
                                classes: {
                                  root: classes.InputRoot,
                                  input: classes.Input,
                                },
                              }}
                              error={hasError}
                              FormHelperTextProps={{
                                component: "div",
                              }}
                              helperText={
                                <HelperText
                                  field={field}
                                  value={value}
                                  row={row}
                                  form={{
                                    formRowIndex,
                                    formHelperText: formHelperText as any,
                                    errors,
                                  }}
                                />
                              }
                              {...otherProps}
                              fullWidth
                              disabled={disabled}
                              multiline
                              minRows={3}
                            />
                          )}
                        />
                      </Box>
                    </Paper>
                  </Fade>
                )}
              </Popper>
            </Box>
          </ClickAwayListener>
        </Box>
      );
    default:
      return null;
  }
};

export default GenericTableEditableField;
