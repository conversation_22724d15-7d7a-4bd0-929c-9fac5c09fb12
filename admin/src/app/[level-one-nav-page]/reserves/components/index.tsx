"use client";;
import Page from "@components/layout/containers/page";
import ReservesComponent from "./reserves";
import { AdminBookResponse, AdminOrganizationResponse, AdminReserveQueryResponse } from "@rubiconcarbon/shared-types";

import type { JSX } from "react";

export default function Reserves({
  reservesResponse,
  defaultPortofolioResponse,
  organizationsResponse,
}: {
  reservesResponse: AdminReserveQueryResponse;
  defaultPortofolioResponse: AdminBookResponse;
  organizationsResponse: AdminOrganizationResponse[];
}): JSX.Element {
  return (
    <Page>
      <ReservesComponent
        reservesResponse={reservesResponse}
        defaultPortofolioResponse={defaultPortofolioResponse}
        organizationsResponse={organizationsResponse}
      />
    </Page>
  );
}
