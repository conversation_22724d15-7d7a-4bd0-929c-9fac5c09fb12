"use client";;
import { AdminGroupingParentResponse } from "@rubiconcarbon/shared-types";
import Page from "@components/layout/containers/page";
import TransferAssetsComponent from "./transfer-assets";

import type { JSX } from "react";

const TransferAssets = ({ booksByParents }: { booksByParents: AdminGroupingParentResponse[] }): JSX.Element => {
  return (
    <Page>
      <TransferAssetsComponent booksByParents={booksByParents} />
    </Page>
  );
};

export default TransferAssets;
