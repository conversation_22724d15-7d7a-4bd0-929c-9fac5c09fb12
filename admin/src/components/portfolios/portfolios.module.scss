.thumbnail {
  img {
    height: 36px;
    width: 36px;
    object-fit: cover;
    border-radius: 30%;
    border: solid 1px grey;
  }
}

.zero-horizontal-padding {
  padding-left: 0;
  padding-right: 0;
}

.carousel {
  margin-top: 10px;
  width: 250px;

  img {
    height: 250px;
    width: auto;
  }
}


.EditProjectTypes {
  min-width: 70%;

  .Chip {
    font-size: 12px;
    text-transform: capitalize;
    color: white;
    width: 100%;
    height: 30px;
  }

  .AddedChip {
    background-color: #007C45;
  }

  .RemovedChip {
    background-color: #C90005;
  }

  .AllocatedChip {
    background-color: #0053BF;
  }

  tr {
    display: flex;

    >:nth-child(1) {
      width: 10%;
    }

    >:nth-child(2) {
      width: 30%;
    }

    >:nth-child(3) {
      width: 60%;
    }

    th {
      display: flex;
    }

    td:nth-child(2) {
      padding: 26px 16px;
    }
  }
}

.ConfirmationRoot {
  min-width: 60%;
}