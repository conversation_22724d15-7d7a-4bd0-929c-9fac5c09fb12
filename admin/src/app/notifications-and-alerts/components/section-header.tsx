"use client";;
import { Grid, Typography } from "@mui/material";

import type { JSX } from "react";

import classes from "../styles/subscription-settings.module.scss";

type SectionHeaderProps = {
  label: string;
  subLabel: string;
};

const SectionHeader = ({ label, subLabel }: SectionHeaderProps): JSX.Element => (
  <Grid className={classes.SectionHeader} item container direction="column">
    <Typography className={classes.HeaderLabel}>{label}</Typography>
    <Typography className={classes.SubLabel}>{subLabel}</Typography>
  </Grid>
);

export default SectionHeader;
