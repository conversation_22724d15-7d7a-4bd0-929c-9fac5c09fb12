import React, { useContext, type JSX } from "react";
import { Box, Typography, Table, TableBody, TableCell, TableContainer, TableRow, Grid } from "@mui/material";
import AddIcon from "@mui/icons-material/Add";
import DeleteIcon from "@mui/icons-material/Delete";
import CheckIcon from "@mui/icons-material/Check";
import { useState } from "react";
import { uuid, AdminUpdateRetirementAmountsRequest } from "@rubiconcarbon/shared-types";
import { AxiosContext } from "@providers/axios-provider";
import PortfolioCompositionVintage from "@models/portfolio-composition-vintage";
import integerFormat from "@utils/formatters/integer-format";
import { TransactionProjectVintage } from "@models/transaction-project-vintage";
import { isEmpty } from "lodash";
import ConfirmationModal, { ButtonDef } from "@components/ui/dialogs/confirmation-dialog";
import { BaseDialogProps, DialogBackdrop } from "@models/dialogs";

interface RetirementCompositionModalProps extends BaseDialogProps {
  retirementComposition: TransactionProjectVintage[];
  changesList: PortfolioCompositionVintage[];
  transactionId: uuid;
  sourceId: uuid;
  refreshData: () => void;
}

export default function RetirementCompositionModal({
  isOpen,
  retirementComposition,
  changesList,
  transactionId,
  sourceId,
  onClose,
  refreshData,
  onConfirm,
  onError,
}: RetirementCompositionModalProps): JSX.Element | null {
  const [requestInFlight, setRequestInFlight] = useState(false);
  const { api } = useContext(AxiosContext);

  const submitCompositionHandler = (): void => {
    const payload = retirementComposition.map((vintage) => ({
      projectVintageId: vintage.id,
      amountTransacted: vintage.amountTransacted,
      sourceId,
    }));

    changesList
      ?.filter((c) => c.quantity === 0)
      ?.forEach((change) =>
        payload.push({
          projectVintageId: change.id,
          amountTransacted: 0,
          sourceId,
        }),
      );

    setRequestInFlight(true);
    api
      .patch<AdminUpdateRetirementAmountsRequest>(`admin/retirements/${transactionId}/amounts`, payload)
      .then(() => {
        refreshData();
        onConfirm?.("Successfully updated retirement composition");
        onClose();
      })
      .catch((e) => {
        let message = "Failed to update retirement composition. ";
        if (!isEmpty(e?.response?.data?.message)) {
          message += e?.response?.data?.message;
        }
        console.error(message, e);
        onError?.(message);
      })
      .finally(() => {
        onClose();
        setRequestInFlight(false);
      });
  };

  const dialogButtons: ButtonDef[] = [
    {
      label: "Yes, proceed",
      variant: "contained",
      onClickHandler: submitCompositionHandler,
    },
  ];

  return (
    <>
      <DialogBackdrop requestInFlight={requestInFlight} />
      <ConfirmationModal
        isOpen={isOpen}
        onClose={onClose}
        title={"Confirm Retirement Composition Update"}
        dialogButtons={dialogButtons}
      >
        <Typography variant="body1">Are you sure you want to save the following changes?</Typography>
        <Box mt={3}>
          <TableContainer sx={{ width: 750, maxHeight: 500 }}>
            <Table aria-label="composition summary table">
              <TableBody>
                {changesList?.map((row) => (
                  <TableRow key={row.id} sx={{ "& > *": { borderBottom: "unset" } }}>
                    <TableCell align="left">{row.name}</TableCell>
                    <TableCell align="left">
                      {row.quantity === 0 ? (
                        <Grid style={{ display: "flex", color: "#094436" }}>
                          <DeleteIcon sx={{ paddingBottom: "4px" }} />
                          <Typography variant="body2">Removed {integerFormat(row.originalQuantity)}</Typography>
                        </Grid>
                      ) : row.originalQuantity ? (
                        <Grid style={{ display: "flex", color: "#094436" }}>
                          <CheckIcon sx={{ paddingBottom: "4px" }} />
                          <Typography variant="body2">
                            Updated from {integerFormat(row.originalQuantity)} to {integerFormat(row.quantity)}{" "}
                          </Typography>
                        </Grid>
                      ) : (
                        <Grid style={{ display: "flex", color: "#094436" }}>
                          <AddIcon sx={{ paddingBottom: "4px" }} />
                          <Typography variant="body2">Added {integerFormat(row.quantity)}</Typography>
                        </Grid>
                      )}
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </Box>
      </ConfirmationModal>
    </>
  );
}
