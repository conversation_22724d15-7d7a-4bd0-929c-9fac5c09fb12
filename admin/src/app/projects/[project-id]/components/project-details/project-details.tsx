import { AdminProjectResponse } from "@rubiconcarbon/shared-types";
import { Stack } from "@mui/material";
import ProjectTabs from "./project-tabs/project-tabs";
import ProjectDetailsHeader from "./project-details-header";

import type { JSX } from "react";

export default function ProjectDetails(props: { project: AdminProjectResponse }): JSX.Element {
  const { project } = props;

  return (
    <>
      <Stack direction="column" gap={2}>
        <ProjectDetailsHeader project={project} />
        <ProjectTabs project={project} />
      </Stack>
    </>
  );
}
