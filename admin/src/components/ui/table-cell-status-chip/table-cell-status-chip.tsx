import MatIcon from "@/components/ui/mat-icon/mat-icon";
import { Box, Chip, SxProps } from "@mui/material";
import { AllKeys, pickFromRecord, classcat, Maybe, toBoolean } from "@rubiconcarbon/frontend-shared";

import type { JSX } from "react";

import classes from "./style.module.scss";

type OrgStatusChipProps<T> = {
  data: T;
  path: {
    value: AllKeys<T>;
    label: AllKeys<T>;
  };
  withIcon?: boolean;
  conditionalSx?: (
    value: boolean,
    label: string,
  ) => {
    chip?: SxProps;
    icon?: SxProps;
    label?: SxProps;
  };
};

export const TableCellStatusChip = <T,>({
  data,
  path,
  withIcon = false,
  conditionalSx,
}: OrgStatusChipProps<T>): JSX.Element => {
  const value = toBoolean((pickFromRecord(data, [path.value as any], true) as any)?.[path.value as any]);
  const label = (pickFromRecord(data, [path.label as any], true) as any)?.[path.label as any] as string;
  const { chip = {}, icon = {}, label: sxLabel = {} } = conditionalSx?.(value, label) ?? {};

  return (
    <Chip
      classes={{
        root: classes.Chip,
        label: classcat({
          [classes.LabelWithIcon]: withIcon,
        }),
      }}
      sx={{
        color: value ? "rgba(7, 125, 85, 1)" : "rgba(211, 47, 47, 1)",
        backgroundColor: value ? "rgba(237, 247, 237, 1)" : "rgba(253, 237, 237, 1)",
        ...chip,
      }}
      icon={
        <Maybe condition={withIcon}>
          <MatIcon
            value={value ? "check" : "clear"}
            size={20}
            sx={{
              color: value ? "rgba(7, 125, 85, 1) !important" : "rgba(211, 47, 47, 1) !important",
              padding: "0 2px 0 5px",
              boxSizing: "content-box",
              ...icon,
            }}
          />
        </Maybe>
      }
      label={<Box sx={sxLabel}>{label}</Box>}
    />
  );
};
