"use client";;
import Page from "@components/layout/containers/page";
import { AdminRetirementResponse } from "@rubiconcarbon/shared-types";
import RetirementCompositionComponent from "./retirement-composition";

import type { JSX } from "react";

const RetirementCompositionPage = ({ retirementResponse }: { retirementResponse: AdminRetirementResponse }): JSX.Element => {
  return (
    <Page>
      <RetirementCompositionComponent retirementResponse={retirementResponse} />
    </Page>
  );
};

export default RetirementCompositionPage;
