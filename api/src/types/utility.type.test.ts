import { RequireType } from './utility.type';
import { IsOptional } from 'class-validator';

// Test class with optional properties
class TestDto {
  @IsOptional()
  name?: string;

  @IsOptional()
  email?: string;

  id!: string; // This is already required
}

// Create a version where all fields are required
class RequiredTestDto extends RequireType(TestDto) {}

// Type-level tests (these would cause TypeScript errors if RequireType doesn't work)
const testInstance: RequiredTestDto = new RequiredTestDto();

// These assignments should work (all properties are available)
const nameIsRequired: string = testInstance.name; // Should be string, not string | undefined
const emailIsRequired: string = testInstance.email; // Should be string, not string | undefined
const idIsRequired: string = testInstance.id; // Was already required

// Test passes if TypeScript compilation succeeds
export { RequiredTestDto, testInstance, nameIsRequired, emailIsRequired, idIsRequired };
