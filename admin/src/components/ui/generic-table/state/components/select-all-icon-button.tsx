import { useAtomValue, useSet<PERSON>tom, useStore } from "jotai";
import {
  filteredRowsAtom,
  selectAllValue<PERSON>tom,
  selectedRowsAtom,
  updateSelectAll<PERSON>tom,
  updateSelectedRowsAtom,
} from "../actions";
import { Checkbox } from "@mui/material";
import { MouseEvent, type JSX } from "react";
import { externalAtom } from "../structure";
import { GenericTableRowModel } from "../../types/generic-table-row-model";
import { GenericTableExternal } from "../../types/generic-table-external";

const canSelect = <M,>(
  row: GenericTableRowModel<M>,
  isSelectable: boolean | ((row: GenericTableRowModel<M>) => boolean),
): boolean => (isSelectable ? (typeof isSelectable === "boolean" ? isSelectable : isSelectable?.(row)) : false);

const SelectAllIconButton = <M,>(): JSX.Element => {
  const store = useStore();

  const { isSelectable } = useAtomValue<GenericTableExternal<M>>(externalAtom, { store });
  const value = useAtomValue(selectAllValueAtom, { store });
  const rows = useAtomValue(filteredRowsAtom, { store });
  const selectedRowsRecord = useAtomValue(selectedRowsAtom, { store });

  const updateSelectedRows = useSetAtom(updateSelectedRowsAtom, { store });
  const set = useSetAtom(updateSelectAllAtom, { store });

  return (
    <Checkbox
      indeterminate={value === "intermediate"}
      checked={value === "all"}
      onClick={(event: MouseEvent<HTMLButtonElement>) => {
        event?.preventDefault();
        event?.stopPropagation();

        const setValue = value === "all" ? "none" : "all";

        rows?.forEach((row) => {
          if (canSelect(row, isSelectable)) {
            if (setValue === "all" && !selectedRowsRecord?.[row?.id]) updateSelectedRows({ id: row?.id });
            else if (setValue === "none" && selectedRowsRecord?.[row?.id]) updateSelectedRows({ id: row?.id });
          }
        });

        set(setValue);
      }}
    />
  );
};

export default SelectAllIconButton;
