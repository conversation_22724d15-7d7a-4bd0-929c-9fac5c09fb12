"use client";;
import Page from "@components/layout/containers/page";
import { AdminRetirementResponse } from "@rubiconcarbon/shared-types";
import RetirementUploadComponent from "./retirement-upload";

import type { JSX } from "react";

export default function RetirementUpload({
  retirementResponse,
}: {
  retirementResponse: AdminRetirementResponse;
}): JSX.Element {
  return (
    <Page>
      <RetirementUploadComponent retirementResponse={retirementResponse} />
    </Page>
  );
}
