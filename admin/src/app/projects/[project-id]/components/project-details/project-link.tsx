import COLORS from "@components/ui/theme/colors";
import { uuid } from "@rubiconcarbon/shared-types";
import Link from "next/link";
import { CSSProperties, type JSX } from "react";

const ProjectLink = ({
  projectId,
  style,
  content,
}: {
  projectId: uuid;
  content: string;
  style?: CSSProperties;
}): JSX.Element => {
  return (
    <Link
      href={`/projects/${projectId}`}
      style={{
        ...style,
        color: COLORS.rubiconGreen,
        cursor: "pointer",
      }}
      onClick={(e) => e.stopPropagation()}
    >
      {content}
    </Link>
  );
};

export default ProjectLink;
