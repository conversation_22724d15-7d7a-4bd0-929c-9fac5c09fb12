import { useLogger } from "@providers/logging";
import useSnackbarVariants from "@hooks/use-enqueue-variant";
import useNavigation from "@/hooks/use-navigation";
import { useBoolean, usePrevious } from "react-use";
import { classValidatorResolver } from "@hookform/resolvers/class-validator";
import { Controller, useFieldArray } from "react-hook-form";
import { useTriggerRequest } from "@rubiconcarbon/frontend-shared";
import {
  AdminRetirementRequest,
  AssetType,
  AdminAssetTypeQuery,
  RetirementType,
  uuid,
  AdminOrganizationQueryResponse,
  AdminOrganizationResponse,
  AdminGroupedAllocationWithNestedResponse,
} from "@rubiconcarbon/shared-types";
import { useEffect, useMemo, type JSX } from "react";
import { KeyboardArrowRightRounded } from "@mui/icons-material";
import { Container, Stack, Autocomplete, TextField, Button } from "@mui/material";
import useAutoCompleteOptions, { UseAutoCompleteOptionsReturnEntry } from "@hooks/use-auto-complete-options";
import { MISSING_DATA } from "@constants/constants";
import GenericDialog from "@components/ui/generic-dialog/generic-dialog";
import { toDecimal, toNumber } from "@rubiconcarbon/frontend-shared";
import { TransactionModel } from "@models/transaction";
import { getNewTransferOutflowModel } from "@utils/helpers/transaction/get-new-transaction-models";
import TransferProductDetails from "./transfer-product-details";
import TransferConfirmation from "./transfer-confirmation";
import { useEnhancedForm } from "@/hooks/use-enhanced-form";

import classes from "../../styles/new-form.module.scss";

const parserBlacklist = ["$", ","];

const TransferModelResolver = classValidatorResolver(TransactionModel);

const NewTransferForm = ({
  organizationsResponse,
}: {
  organizationsResponse: AdminOrganizationQueryResponse;
}): JSX.Element => {
  const { logger } = useLogger();
  const { popFromPath } = useNavigation();
  const { enqueueSuccess, enqueueError } = useSnackbarVariants();

  const [canConfirm, setCanConfirm] = useBoolean(false);

  const model = getNewTransferOutflowModel();

  const {
    control,
    formState: { errors },
    trigger,
    smartSetValue,
    watch,
    resetField,
    handleSubmit,
  } = useEnhancedForm<TransactionModel>({
    resolver: TransferModelResolver,
    defaultValues: model,
    mode: "onSubmit",
  });

  const {
    fields: lineItems,
    append,
    remove,
  } = useFieldArray({
    control,
    name: "orders",
  });

  const data = watch();
  const organization = data?.transfer?.organization;

  const previousSelectedOrgnizationId = usePrevious(organization?.id);

  const {
    data: assetsResponse,
    trigger: getAssets,
    isMutating: loadingAssets,
  } = useTriggerRequest<AdminGroupedAllocationWithNestedResponse, object, { id: uuid }, AdminAssetTypeQuery>({
    url: "admin/organizations/{id}/holdings",
    pathParams: {
      id: organization?.id,
    },
    queryParams: {
      assetTypes: [AssetType.REGISTRY_VINTAGE],
    },
    swrOptions: {
      onError: (error: any): void => {
        enqueueError(`Unable to fetch assets for ${organization?.name}`);
        logger.error(`Unable to load assets for ${organization?.name}: ${error?.message}`, {});
      },
    },
  });

  const { trigger: commitTransfer, isMutating: commitingRetirement } = useTriggerRequest<
    object,
    AdminRetirementRequest
  >({
    url: "admin/retirements",
    method: "post",
    requestBody: {
      organizationId: data?.transfer?.organization?.id,
      isPublic: false,
      assetType: AssetType.REGISTRY_VINTAGE,
      beneficiary: MISSING_DATA,
      type: RetirementType.TRANSFER_OUTFLOW,
      assets: data?.orders?.map((item) => ({
        assetId: item?.supplementaryAssetDetails?.id,
        sourceId: data?.transfer?.organization?.id,
        amount: toNumber(item?.amount, { parserBlacklist }),
        rawPrice: toDecimal(0.0),
      })),
      registryAccount: data?.transfer?.registryAccount,
    },
    swrOptions: {
      onSuccess: () => {
        enqueueSuccess("Transfer created successfully.");
        popFromPath(1);
      },
      onError: (error: any): void => {
        enqueueError("Unable to create transfer.");
        logger.error(`Unable to create transfer: ${error?.message}`, {});
      },
    },
  });

  const allocations = useMemo(() => assetsResponse?.allocations || [], [assetsResponse?.allocations]);

  useEffect(() => {
    const organizationId = organization?.id;
    if (!!organizationId && previousSelectedOrgnizationId !== organizationId && !loadingAssets)
      setTimeout(async () => await getAssets());
  }, [organization?.id, getAssets, loadingAssets, previousSelectedOrgnizationId]);

  const organizationOptions = useAutoCompleteOptions<AdminOrganizationResponse, AdminOrganizationResponse>({
    data: organizationsResponse?.data || [],
    keys: ["id", "name", "customerPortfolio"],
    label: (entry: Partial<AdminOrganizationResponse>) => entry?.name || '',
    value: (entry: Partial<AdminOrganizationResponse>) => entry as AdminOrganizationResponse,
    preTransform: (data: AdminOrganizationResponse[]) => data?.filter(({ isEnabled }) => isEnabled),
    postTransform: (data: UseAutoCompleteOptionsReturnEntry[]) =>
      data?.sort((a, b) => a?.label?.localeCompare(b?.label)),
  });

  const onSubmit = (): void => setCanConfirm(true);

  return (
    <Container sx={{ padding: "20px 0px" }}>
      <Stack
        component="form"
        justifyContent="center"
        gap={3}
        maxWidth={1200}
        minWidth={900}
        onSubmit={handleSubmit(onSubmit)}
      >
        <Controller
          name="transfer.organization"
          control={control}
          render={({ field: { ref, value, onChange, ...otherProps } }): JSX.Element => {
            const selectedOption = organizationOptions.find((entry) => entry?.value?.id === value?.id) ?? null;
            return (
              <Autocomplete
                options={organizationOptions}
                value={selectedOption}
                onChange={(_, selection) => onChange(selection?.value)}
                id="organization"
                getOptionKey={(option: UseAutoCompleteOptionsReturnEntry<AdminOrganizationResponse>) =>
                  option?.value?.id
                }
                getOptionLabel={(option: UseAutoCompleteOptionsReturnEntry<AdminOrganizationResponse>) => option?.label}
                renderInput={({ InputProps, ...params }) => (
                  <TextField
                    {...params}
                    InputProps={{
                      ref,
                      ...InputProps,
                    }}
                    label="Organization"
                    {...otherProps}
                    error={!!errors?.transfer?.organization}
                    helperText={errors?.transfer?.organization?.message}
                    fullWidth
                  />
                )}
                fullWidth
              />
            );
          }}
        />
        <TransferProductDetails
          loadingAssets={loadingAssets}
          allocations={allocations}
          control={control}
          errors={errors}
          lineItems={lineItems}
          trigger={trigger}
          setValue={smartSetValue}
          resetField={resetField}
          watch={watch}
          append={append}
          remove={remove}
        />
        <Controller
          name="transfer.registryAccount"
          control={control}
          render={({ field: { ref, value, ...otherProps } }): JSX.Element => (
            <TextField
              label="Transfer Credits to Account"
              value={value ?? ""}
              InputProps={{ ref }}
              {...otherProps}
              fullWidth
              multiline
            />
          )}
        />
        <Stack direction="row" justifyContent="space-between">
          <Button className={classes.ActionButton} color="error" onClick={() => popFromPath(1)}>
            Cancel
          </Button>
          <Button
            className={classes.ActionButton}
            type="submit"
            variant="contained"
            endIcon={<KeyboardArrowRightRounded />}
            disabled={loadingAssets}
          >
            Continue: Confirmation
          </Button>
        </Stack>
      </Stack>
      <GenericDialog
        title="Review and Confirm Transfer"
        open={canConfirm}
        onClose={() => setCanConfirm(false)}
        positiveAction={{
          buttonText: "CONFIRM TRANSFER",
          loading: commitingRetirement,
          onClick: async () => await commitTransfer(),
        }}
        negativeAction={{
          buttonText: "CANCEL",
          onClick: () => setCanConfirm(false),
        }}
        classes={{
          root: classes.Dialog,
          title: classes.Title,
          content: classes.Content,
          actions: classes.Actions,
        }}
      >
        <TransferConfirmation data={data} />
      </GenericDialog>
    </Container>
  );
};

export default NewTransferForm;
