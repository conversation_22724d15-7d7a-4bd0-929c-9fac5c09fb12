import 'reflect-metadata';

/**
 * RequireType utility class that makes all properties required (opposite of PartialType)
 * Similar to NestJS PartialType but ensures all fields are required instead of optional
 */
export function RequireType<T>(classRef: new (...args: any[]) => T): new (...args: any[]) => Required<T> {
  abstract class RequiredClass {
    constructor() {
      // Copy all properties from the base class but make them required
      const instance = new classRef();
      Object.assign(this, instance);
    }
  }

  // Copy metadata from the original class for proper decorators handling
  const descriptors = Object.getOwnPropertyDescriptors(classRef.prototype);
  Object.keys(descriptors).forEach((key) => {
    if (key !== 'constructor') {
      Object.defineProperty(RequiredClass.prototype, key, descriptors[key]);
    }
  });

  // Copy class metadata for class-transformer and class-validator
  if (typeof Reflect !== 'undefined' && 'getMetadataKeys' in Reflect) {
    const metadataKeys = (Reflect as any).getMetadataKeys(classRef);
    metadataKeys.forEach((key: any) => {
      const metadata = (Reflect as any).getMetadata(key, classRef);
      (Reflect as any).defineMetadata(key, metadata, RequiredClass);
    });

    // Copy property metadata
    Object.getOwnPropertyNames(classRef.prototype).forEach((propertyName) => {
      if (propertyName !== 'constructor') {
        const propertyMetadataKeys = (Reflect as any).getMetadataKeys(classRef.prototype, propertyName);
        propertyMetadataKeys.forEach((key: any) => {
          const metadata = (Reflect as any).getMetadata(key, classRef.prototype, propertyName);
          (Reflect as any).defineMetadata(key, metadata, RequiredClass.prototype, propertyName);
        });
      }
    });
  }

  return RequiredClass as new (...args: any[]) => Required<T>;
}

/**
 * Example usage of RequireType:
 *
 * // For a class with optional properties:
 * class UserDto {
 *   @IsOptional()
 *   name?: string;
 *
 *   @IsOptional()
 *   email?: string;
 * }
 *
 * // Create a version where all fields are required:
 * class RequiredUserDto extends RequireType(UserDto) {}
 *
 * // Now all properties in RequiredUserDto are required (not optional)
 * // This is useful when you want to ensure all fields are provided
 * // as opposed to PartialType which makes all fields optional
 */
