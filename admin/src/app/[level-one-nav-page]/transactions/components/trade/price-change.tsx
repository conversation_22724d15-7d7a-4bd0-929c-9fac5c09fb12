import { ArrowUpwardRounded } from "@mui/icons-material";
import { Stack, Typography } from "@mui/material";
import { Maybe, numberFormat } from "@rubiconcarbon/frontend-shared";
import { AdminVintagePricingResponse } from "@rubiconcarbon/shared-types";
import Decimal from "decimal.js";

import type { JSX } from "react";

import classes from "../../styles/price-change.module.scss";

type PriceChangeProps = {
  type: "7" | "30";
  pricing: AdminVintagePricingResponse;
};

const PriceChange = ({ type, pricing }: PriceChangeProps): JSX.Element => {
  const price = pricing[`change_${type}`];
  const percentage = new Decimal(pricing[`perc_change_${type}`]).times(100);
  const noChange = `${price?.toFixed(2)}` === "0.00";
  const hasGain = +price > 0;

  return (
    <Stack
      className={`${classes.Chip}${!noChange && hasGain ? ` ${classes.Gain}` : ""}${!noChange && !hasGain ? ` ${classes.Loss}` : ""}`}
      direction="row"
      justifyContent="flex-start"
      alignItems="center"
      gap={1}
    >
      <Maybe condition={!noChange}>
        <ArrowUpwardRounded className={`${classes.ChangeIcon}${hasGain ? ` ${classes.Gain}` : ` ${classes.Loss}`}`} />
        <Typography className={classes.Label} variant="subtitle2">
          {hasGain ? "+" : "-"}
          {numberFormat(Math.abs(+price), { decimalPlaces: 2, prepend: "$" })}
        </Typography>
        <Typography className={classes.Label} variant="subtitle2">
          ({hasGain ? "+" : ""}
          {numberFormat(+percentage, { decimalPlaces: 2, append: "%" })})
        </Typography>
        <Typography className={classes.Label} variant="subtitle2">
          {type} days
        </Typography>
      </Maybe>
      <Maybe condition={noChange}>
        <Typography className={classes.Label} variant="subtitle2">
          No change over last {type} days
        </Typography>
      </Maybe>
    </Stack>
  );
};

export default PriceChange;
