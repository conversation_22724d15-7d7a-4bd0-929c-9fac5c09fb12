"use client";;
import Page from "@components/layout/containers/page";
import ForwardDeliveryComponent from "./forward-delivery";
import {
  AdminForwardQueryResponse,
  AdminOrganizationQueryResponse,
  AdminGroupingParentQueryResponse,
} from "@rubiconcarbon/shared-types";

import type { JSX } from "react";

const ForwardDeliveryPage = ({
  forwardsResponse,
  organizationsResponse,
  booksByParentResponse,
  type,
  search,
}: {
  forwardsResponse: AdminForwardQueryResponse;
  organizationsResponse: AdminOrganizationQueryResponse;
  booksByParentResponse: AdminGroupingParentQueryResponse;
  type?: "buy" | "sell";
  search?: string;
}): JSX.Element => {
  return (
    <Page>
      <ForwardDeliveryComponent
        forwardsResponse={forwardsResponse}
        organizationsResponse={organizationsResponse}
        booksByParentResponse={booksByParentResponse}
        type={type}
        search={search}
      />
    </Page>
  );
};

export default ForwardDeliveryPage;
