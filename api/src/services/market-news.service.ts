/* third party */
import { FilterQuery, LoadStrategy, LockMode, UniqueConstraintViolationException } from '@mikro-orm/core';
import { EntityManager } from '@mikro-orm/postgresql';
import { ConflictException, Injectable, UnprocessableEntityException } from '@nestjs/common';
/* rubicon */
import { AuditLogAction, MarketNewsScope, uuid } from '@rubiconcarbon/shared-types';
/* app */
import { UserClaims } from '@app/auth';
import { MarketNews, Organization, Project, User } from '@app/entities';
import { AuditLogsService } from '@app/utility/audit-log';
import {
  AdminMarketNewsCreateRequestDTO,
  AdminMarketNewsQueryDTO,
  AdminMarketNewsTagRequestDTO,
  AdminMarketNewsUpdateRequestDTO,
  PortalMarketNewsQueryDTO,
} from '@app/dtos/market-news.dto';
import { OrderByDirectionSQL } from '@app/enums/orderby.enum';
import { InternalMarketNewsQueryResponse } from '@app/interfaces/market-news.interface';

@Injectable()
export class MarketNewsService {
  constructor(
    private em: EntityManager,
    private auditLogsService: AuditLogsService,
  ) {}

  async create(claims: UserClaims, data: AdminMarketNewsCreateRequestDTO): Promise<MarketNews> {
    const user = await this.em.findOneOrFail(User, { id: claims.id });
    const now = new Date();
    const id: uuid = uuid();
    const action = AuditLogAction.MARKET_NEWS_CREATED;
    return await this.em.transactional(async (tx) => {
      if (data.organizationId) {
        await this.em.findOneOrFail(Organization, { id: data.organizationId });
      }
      const marketNews: MarketNews = tx.create(MarketNews, {
        ...data,
        id,
        createdAt: now,
        updatedAt: now,
        createdBy: user,
        updatedBy: user,
        isDeleted: false,
      });

      try {
        await this.auditLogsService.create(tx, claims, action, now, id, { ...marketNews });
        return marketNews;
      } catch (e) {
        if (e instanceof UniqueConstraintViolationException) throw new ConflictException('url already exists');
        throw e;
      }
    });
  }

  async tag(claims: UserClaims, id: uuid, data: AdminMarketNewsTagRequestDTO): Promise<MarketNews> {
    return this.em.transactional(async (tx) => {
      let projects: Project[] = [];
      const now = new Date();

      const marketNews: MarketNews = await tx.findOneOrFail(
        MarketNews,
        { id },
        { lockMode: LockMode.PESSIMISTIC_WRITE },
      );

      if (data.projectIds.length > 0) {
        projects = await tx.find(Project, { id: { $in: data.projectIds } });
        const missing = data.projectIds.filter((projectId) => !projects.map((dp) => dp.id).includes(projectId));
        if (missing.length > 0) {
          throw new UnprocessableEntityException(`Project ids ${missing.join(', ')} are not valid`);
        }
        marketNews.projects.set(projects);
      } else {
        marketNews.projects.removeAll();
      }

      const user = await tx.findOneOrFail(User, { id: claims.id });
      marketNews.updatedAt = now;
      marketNews.updatedBy = user;

      const action = AuditLogAction.MARKET_NEWS_TAGGED;
      await this.auditLogsService.create(tx, claims, action, now, marketNews.id, { ...marketNews });

      return marketNews;
    });
  }

  async update(claims: UserClaims, id: uuid, data: AdminMarketNewsUpdateRequestDTO): Promise<MarketNews> {
    return this.em.transactional(async (tx) => {
      const user = await this.em.findOneOrFail(User, { id: claims.id });
      const now = new Date();
      const action = AuditLogAction.MARKET_NEWS_UPDATED;
      const marketNews: MarketNews = await tx.findOneOrFail(MarketNews, { id }, { populate: ['projects'] });
      if (data.organizationId) {
        await this.em.findOneOrFail(Organization, { id: data.organizationId });
        marketNews.organizationId = data.organizationId;
      }
      if (data.articleDate) {
        marketNews.articleDate = data.articleDate;
      }
      if (data.header) {
        marketNews.header = data.header;
      }
      if (data.scopes) {
        marketNews.scopes = data.scopes;
      }
      if (data.summary) {
        marketNews.summary = data.summary;
      }
      if (data.url) {
        marketNews.url = data.url;
      }
      if (data.source) {
        marketNews.source = data.source;
      }
      if (data.score) {
        marketNews.score = data.score;
      }
      if (data.hitword) {
        marketNews.hitword = data.hitword;
      }
      if (data.isIrrelevant !== undefined) {
        marketNews.isIrrelevant = data.isIrrelevant;
      }
      marketNews.updatedAt = now;
      marketNews.updatedBy = user;
      await this.auditLogsService.create(tx, claims, action, now, marketNews.id, { ...marketNews });
      return marketNews;
    });
  }

  async delete(claims: UserClaims, id: uuid): Promise<void> {
    await this.em.transactional(async (tx) => {
      const now = new Date();
      const user = await tx.findOneOrFail(User, { id: claims.id });
      const marketNews = await tx.findOneOrFail(MarketNews, { id });
      marketNews.updatedAt = now;
      marketNews.updatedBy = user;
      marketNews.isDeleted = true;
      await this.auditLogsService.create(tx, claims, AuditLogAction.MODEL_PORTFOLIO_DELETED, now, id, {
        ...marketNews,
      });
    });
  }

  async findAllPortal(query: PortalMarketNewsQueryDTO): Promise<InternalMarketNewsQueryResponse> {
    const where: FilterQuery<MarketNews> = {
      isDeleted: false,
      isIrrelevant: false,
      scopes: { $overlap: [MarketNewsScope.CUSTOMER_PORTAL] },
    };
    if (query.endDate) {
      where.articleDate = { $lte: query.endDate };
    }
    if (query.hitword) {
      where.hitword = query.hitword;
    }
    if (query.projectIds) {
      where.projects = { id: query.projectIds };
    }
    if (query.source) {
      where.source = query.source;
    }
    if (query.startDate) {
      where.articleDate = { $gte: query.startDate };
    }

    const count = query.includeTotalCount ? await this.em.count(MarketNews, where) : undefined;
    const data = await this.em.find(MarketNews, where, {
      limit: query.limit,
      offset: query.offset,
      orderBy: { [query.orderBy]: OrderByDirectionSQL[query.orderByDirection] },
      populate: ['createdBy', 'updatedBy', 'projects'],
      strategy: LoadStrategy.JOINED,
      connectionType: 'read',
    });
    return {
      data,
      page: {
        limit: query.limit,
        offset: query.offset,
        size: data.length,
        totalCount: count,
      },
    };
  }

  async findAllAdmin(query: AdminMarketNewsQueryDTO): Promise<InternalMarketNewsQueryResponse> {
    const where: FilterQuery<MarketNews> = { isDeleted: false };
    if (query.endDate) {
      where.articleDate = { $lte: query.endDate };
    }
    if (query.organizationId) {
      await this.em.findOneOrFail(Organization, { id: query.organizationId });
      where.organizationId = query.organizationId;
    }
    if (query.hitword) {
      where.hitword = query.hitword;
    }
    if (query.projectIds) {
      where.projects = { id: query.projectIds };
    }
    if (query.scopes) {
      where.scopes = { $overlap: query.scopes };
    }
    if (query.source) {
      where.source = query.source;
    }
    if (query.startDate) {
      where.articleDate = { $gte: query.startDate };
    }

    const count = query.includeTotalCount ? await this.em.count(MarketNews, where) : undefined;
    const data = await this.em.find(MarketNews, where, {
      limit: query.limit,
      offset: query.offset,
      orderBy: { [query.orderBy]: OrderByDirectionSQL[query.orderByDirection] },
      strategy: LoadStrategy.JOINED,
      populate: ['createdBy', 'updatedBy', 'projects'],
      connectionType: 'read',
    });

    return {
      data,
      page: {
        limit: query.limit,
        offset: query.offset,
        size: data.length,
        totalCount: count,
      },
    };
  }

  async findOne(id: uuid): Promise<MarketNews> {
    return await this.em.findOneOrFail(
      MarketNews,
      { id, isDeleted: false },
      {
        strategy: LoadStrategy.JOINED,
        connectionType: 'read',
        populate: ['createdBy', 'updatedBy', 'projects'],
      },
    );
  }
}
