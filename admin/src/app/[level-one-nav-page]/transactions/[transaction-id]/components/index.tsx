"use client";;
import { AdminPurchaseResponse, AdminTradeResponse, AdminTradeConfirmationResponse } from "@rubiconcarbon/shared-types";
import TransactionDetailsComponent from "./details";

import type { JSX } from "react";

export default function TransactionDetails({
  assetResponse,
  tradeConfirmationResponse,
  type,
}: {
  assetResponse: AdminPurchaseResponse | AdminTradeResponse;
  tradeConfirmationResponse?: AdminTradeConfirmationResponse;
  type: string;
}): JSX.Element {
  return (
    <TransactionDetailsComponent
      assetResponse={assetResponse}
      tradeConfirmationResponse={tradeConfirmationResponse}
      type={type}
    />
  );
}
