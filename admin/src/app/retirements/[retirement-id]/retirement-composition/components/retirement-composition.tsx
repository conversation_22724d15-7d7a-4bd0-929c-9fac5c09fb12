import React, { useState, useEffect, useMemo, type JSX } from "react";
import {
  AdminRctAssetRetirementResponse,
  AdminRetirementQuery,
  RetirementRelations,
  AdminRetirementResponse,
  uuid,
} from "@rubiconcarbon/shared-types";
import RetirementCompositionTable from "./retirement-composition-table";
import { TransactionProjectVintage, mapTransactionProjectVintageData } from "@models/transaction-project-vintage";
import { useTriggerRequest } from "@rubiconcarbon/frontend-shared";
import useSnackbarVariants from "@hooks/use-enqueue-variant";
import { useLogger } from "@providers/logging";

export default function RetirementComposition({
  retirementResponse: serverRetirementResponse,
}: {
  retirementResponse: AdminRetirementResponse;
}): JSX.Element {
  const { logger } = useLogger();
  const { enqueueError } = useSnackbarVariants();

  const { id: retirementId } = serverRetirementResponse || {};
  const [projectVintages, setProjectVintages] = useState<TransactionProjectVintage[]>();

  const { data: retirementResponse, trigger: refreshRetirement } = useTriggerRequest<
    AdminRetirementResponse,
    object,
    object,
    AdminRetirementQuery
  >({
    url: `/admin/retirements/${retirementId}`,
    queryParams: {
      includeRelations: [
        RetirementRelations.CUSTOMER_PORTFOLIO,
        RetirementRelations.ASSETS,
        RetirementRelations.RCT_VINTAGES,
      ],
    },
    optimisticData: serverRetirementResponse,
    swrOptions: {
      onError: (error: any): void => {
        enqueueError("Unable to fetch retirement.");
        logger.error(`Unable to fetch retirement with id ${retirementId}. Error: ${error?.message}`, {});
      },
    },
  });

  const asset = useMemo(() => retirementResponse?.assets?.[0] as AdminRctAssetRetirementResponse, [retirementResponse?.assets]);

  useEffect(() => {
    if (asset) {
      const mapResult = mapTransactionProjectVintageData(asset?.associatedVintages);
      setProjectVintages(mapResult);
    }
  }, [asset]);

  return (
    <RetirementCompositionTable
      rows={projectVintages ?? []}
      bookId={asset?.asset.id}
      transactionId={retirementResponse?.id as uuid}
      onRefresh={refreshRetirement}
      sourceId={asset?.asset?.id}
    />
  );
}
