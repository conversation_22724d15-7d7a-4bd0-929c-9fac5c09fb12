"use client";
import Page from "@components/layout/containers/page";
import UserActivityTable from "./user-activity-table";
import { AdminUserActionResponse } from "@rubiconcarbon/shared-types";

import type { JSX } from "react";

export default function UserActivity({
  since,
  useActionsDataResponse,
}: {
  since: string;
  useActionsDataResponse: AdminUserActionResponse[];
}): JSX.Element {
  return (
    <Page>
      <UserActivityTable since={since} useActionsDataResponse={useActionsDataResponse} />
    </Page>
  );
}
